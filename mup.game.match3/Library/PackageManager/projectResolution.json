{"context": {"projectPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Packages", "unityVersion": "6000.0.51f1"}, "inputs": ["/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Packages/manifest.json", "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Packages/packages-lock.json", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/BuiltInPackagesCombined.sha1"], "outputs": {"com.annulusgames.lit-motion@https://github.com/annulusgames/LitMotion.git?path=src/LitMotion/Assets/LitMotion": {"name": "com.annulusgames.lit-motion", "displayName": "LitMotion", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.annulusgames.lit-motion@7688ea6127fb", "fingerprint": "7688ea6127fbfdd222f17f4080da55cc75e74e91", "version": "2.0.1", "source": "git", "testable": false}, "com.cysharp.r3@https://github.com/Cysharp/R3.git?path=src/R3.Unity/Assets/R3.Unity": {"name": "com.cysharp.r3", "displayName": "R3", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.cysharp.r3@96346038d239", "fingerprint": "96346038d2394c17468be16d08af95d812692375", "editorCompatibility": "2021.3.0a1", "version": "1.3.0", "source": "git", "testable": false}, "com.cysharp.unitask@https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask": {"name": "com.cysharp.unitask", "displayName": "UniTask", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.cysharp.unitask@86b6e6a2e286", "fingerprint": "86b6e6a2e2863facad59648b5c1bc1b9e91e6514", "editorCompatibility": "2018.4.0a1", "version": "2.5.10", "source": "git", "testable": false}, "com.github-glitchenzo.nugetforunity@https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity": {"name": "com.github-glitchenzo.nugetforunity", "displayName": "NuGetForUnity", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.github-glitchenzo.nugetforunity@21b3bb0ec087", "fingerprint": "21b3bb0ec0878ac8f34ad763c4b0748825f8dbd8", "editorCompatibility": "2018.4.0a1", "version": "4.4.0", "source": "git", "testable": false}, "com.unity.addressables@2.5.0": {"name": "com.unity.addressables", "displayName": "Addressables", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.addressables@2581a6be7421", "fingerprint": "2581a6be742198221d66a5688fd5dff18eff1057", "editorCompatibility": "2023.1.0a1", "version": "2.5.0", "source": "registry", "testable": false}, "com.unity.feature.2d@2.0.1": {"name": "com.unity.feature.2d", "displayName": "2D", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.feature.2d@dd1ea8910f12", "fingerprint": "dd1ea8910f12f021c166e8d0d78de44f1390ff6b", "version": "2.0.1", "source": "builtin", "testable": false}, "com.unity.ide.visualstudio@2.0.23": {"name": "com.unity.ide.visualstudio", "displayName": "Visual Studio Editor", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.ide.visualstudio@198cdf337d13", "fingerprint": "198cdf337d13c83ca953581515630d66b779e92b", "editorCompatibility": "2019.4.25f1", "version": "2.0.23", "source": "registry", "testable": false}, "com.unity.inputsystem@1.14.1": {"name": "com.unity.inputsystem", "displayName": "Input System", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.inputsystem@978211393e28", "fingerprint": "978211393e28699aba338ed157cef347ac565bbf", "editorCompatibility": "2021.3.0a1", "version": "1.14.1", "source": "registry", "testable": false}, "com.unity.project-auditor@1.0.1": {"name": "com.unity.project-auditor", "displayName": "Project Auditor", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.project-auditor@94c6e4e98816", "fingerprint": "94c6e4e9881619d6fd9744d93bd32bec6a2d676c", "editorCompatibility": "2021.3.0a1", "version": "1.0.1", "source": "registry", "testable": false}, "com.unity.render-pipelines.universal@17.0.4": {"name": "com.unity.render-pipelines.universal", "displayName": "Universal Render Pipeline", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.render-pipelines.universal@a55da47cc43f", "fingerprint": "a55da47cc43f82fb2bcd4d2e0c610e473c0f6880", "editorCompatibility": "6000.0.0a1", "version": "17.0.4", "source": "builtin", "testable": false}, "com.unity.ugui@2.0.0": {"name": "com.unity.ugui", "displayName": "Unity UI", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.ugui@a0f5d16b3c82", "fingerprint": "a0f5d16b3c8274d62d6bf6afdaa2eeef242bbd2b", "editorCompatibility": "2019.2.0a1", "version": "2.0.0", "source": "builtin", "testable": false}, "com.unity.modules.accessibility@1.0.0": {"name": "com.unity.modules.accessibility", "displayName": "Accessibility", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.accessibility", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.androidjni@1.0.0": {"name": "com.unity.modules.androidjni", "displayName": "Android JNI", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.androidjni", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.animation@1.0.0": {"name": "com.unity.modules.animation", "displayName": "Animation", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.animation", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.assetbundle@1.0.0": {"name": "com.unity.modules.assetbundle", "displayName": "<PERSON><PERSON>", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.assetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.audio@1.0.0": {"name": "com.unity.modules.audio", "displayName": "Audio", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.audio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imageconversion@1.0.0": {"name": "com.unity.modules.imageconversion", "displayName": "Image Conversion", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imageconversion", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.imgui@1.0.0": {"name": "com.unity.modules.imgui", "displayName": "IMGUI", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imgui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.jsonserialize@1.0.0": {"name": "com.unity.modules.jsonserialize", "displayName": "JSONSerialize", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.jsonserialize", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.particlesystem@1.0.0": {"name": "com.unity.modules.particlesystem", "displayName": "Particle System", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.particlesystem", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics@1.0.0": {"name": "com.unity.modules.physics", "displayName": "Physics", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.physics2d@1.0.0": {"name": "com.unity.modules.physics2d", "displayName": "Physics 2D", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics2d", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.screencapture@1.0.0": {"name": "com.unity.modules.screencapture", "displayName": "Screen Capture", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.screencapture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.ui@1.0.0": {"name": "com.unity.modules.ui", "displayName": "UI", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.ui", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequest@1.0.0": {"name": "com.unity.modules.unitywebrequest", "displayName": "Unity Web Request", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequest", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestassetbundle@1.0.0": {"name": "com.unity.modules.unitywebrequestassetbundle", "displayName": "Unity Web Request Asset Bundle", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestassetbundle", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestaudio@1.0.0": {"name": "com.unity.modules.unitywebrequestaudio", "displayName": "Unity Web Request Audio", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestaudio", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequesttexture@1.0.0": {"name": "com.unity.modules.unitywebrequesttexture", "displayName": "Unity Web Request Texture", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequesttexture", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.modules.unitywebrequestwww@1.0.0": {"name": "com.unity.modules.unitywebrequestwww", "displayName": "Unity Web Request WWW", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestwww", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.render-pipelines.core@17.0.4": {"name": "com.unity.render-pipelines.core", "displayName": "Scriptable Render Pipeline Core", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.render-pipelines.core@2be5e7224a10", "fingerprint": "2be5e7224a10830881453cbaf6898e584c3addb6", "editorCompatibility": "6000.0.0a1", "version": "17.0.4", "source": "builtin", "testable": false}, "com.unity.shadergraph@17.0.4": {"name": "com.unity.shadergraph", "displayName": "Shader Graph", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.shadergraph@052d95cfe62a", "fingerprint": "052d95cfe62aa3664d6a823074c5806f7597e6a4", "editorCompatibility": "6000.0.0a1", "version": "17.0.4", "source": "builtin", "testable": false}, "com.unity.render-pipelines.universal-config@17.0.3": {"name": "com.unity.render-pipelines.universal-config", "displayName": "Universal Render Pipeline Config", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.render-pipelines.universal-config@dd206bf35d04", "fingerprint": "dd206bf35d0451204981fdb31d4c6831a0ee2a7e", "editorCompatibility": "6000.0.0a1", "version": "17.0.3", "source": "builtin", "testable": false}, "com.unity.nuget.mono-cecil@1.11.4": {"name": "com.unity.nuget.mono-cecil", "displayName": "Mono Cecil", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f", "fingerprint": "d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62", "editorCompatibility": "2018.4.0a1", "version": "1.11.4", "source": "registry", "testable": false}, "com.unity.nuget.newtonsoft-json@3.2.1": {"name": "com.unity.nuget.newtonsoft-json", "displayName": "Newtonsoft Json", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0", "fingerprint": "74deb55db2a0c29ddfda576608bcb86abbd13ee6", "editorCompatibility": "2018.4.0a1", "version": "3.2.1", "source": "registry", "testable": false}, "com.unity.modules.uielements@1.0.0": {"name": "com.unity.modules.uielements", "displayName": "UIElements", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.uielements", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.test-framework@1.5.1": {"name": "com.unity.test-framework", "displayName": "Test Framework", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.test-framework@dfdbd02f5918", "fingerprint": "dfdbd02f59180baa5668370b8f71f6fbeaa3b032", "editorCompatibility": "2022.3.0a1", "version": "1.5.1", "source": "builtin", "testable": false}, "com.unity.2d.animation@10.1.4": {"name": "com.unity.2d.animation", "displayName": "2D Animation", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.animation@494a3b4e73a9", "fingerprint": "494a3b4e73a9ae26677ef6e9fd6bff4ca643770a", "editorCompatibility": "2023.1.0a1", "version": "10.1.4", "source": "registry", "testable": false}, "com.unity.2d.pixel-perfect@5.0.3": {"name": "com.unity.2d.pixel-perfect", "displayName": "2D Pixel Perfect", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.pixel-perfect@e3ae982b672d", "fingerprint": "e3ae982b672dc7cca42a6303bdf53b84c69991da", "editorCompatibility": "2021.1.0a1", "version": "5.0.3", "source": "registry", "testable": false}, "com.unity.2d.psdimporter@9.0.3": {"name": "com.unity.2d.psdimporter", "displayName": "2D PSD Importer", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.psdimporter@676bae148e11", "fingerprint": "676bae148e11de9a02db5a3614b8c56e4f0f44ac", "editorCompatibility": "2023.1.0a1", "version": "9.0.3", "source": "registry", "testable": false}, "com.unity.2d.sprite@1.0.0": {"name": "com.unity.2d.sprite", "displayName": "2D Sprite", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.sprite@a1146c20a947", "fingerprint": "a1146c20a94767d627b9db262ae136cedef9c2c7", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.2d.spriteshape@10.0.7": {"name": "com.unity.2d.spriteshape", "displayName": "2D SpriteShape", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.spriteshape@9e35352ae135", "fingerprint": "9e35352ae135f602746220e7edc09eb95bbec530", "editorCompatibility": "2023.1.0a1", "version": "10.0.7", "source": "registry", "testable": false}, "com.unity.2d.tilemap@1.0.0": {"name": "com.unity.2d.tilemap", "displayName": "2D Tilemap Editor", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.tilemap@91e7def251e0", "fingerprint": "91e7def251e0de922ba309ad7aa18299b1d60ca7", "editorCompatibility": "2019.2.0a1", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.2d.tilemap.extras@4.1.0": {"name": "com.unity.2d.tilemap.extras", "displayName": "2D Tilemap Extras", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.tilemap.extras@13634da7dbe0", "fingerprint": "13634da7dbe06c39bac6bbe2d1a166cf91f58ad7", "editorCompatibility": "6000.0.0a1", "version": "4.1.0", "source": "registry", "testable": false}, "com.unity.2d.aseprite@1.1.9": {"name": "com.unity.2d.aseprite", "displayName": "2D Aseprite Importer", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.aseprite@996e69f78764", "fingerprint": "996e69f78764999291cc161902e252662ddc9485", "editorCompatibility": "2021.3.15f1", "version": "1.1.9", "source": "registry", "testable": false}, "com.unity.profiling.core@1.0.2": {"name": "com.unity.profiling.core", "displayName": "Unity Profiling Core API", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.profiling.core@aac7b93912bc", "fingerprint": "aac7b93912bc5df5fe06b04ff1b758493cdc2346", "editorCompatibility": "2020.1.0a1", "version": "1.0.2", "source": "registry", "testable": false}, "com.unity.scriptablebuildpipeline@2.4.0": {"name": "com.unity.scriptablebuildpipeline", "displayName": "Scriptable Build Pipeline", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.scriptablebuildpipeline@7e8a1cf5a47d", "fingerprint": "7e8a1cf5a47d3fd88c96e0cb21b3d805b63b6834", "editorCompatibility": "2023.1.0a1", "version": "2.4.0", "source": "registry", "testable": false}, "com.unity.burst@1.8.21": {"name": "com.unity.burst", "displayName": "<PERSON><PERSON><PERSON>", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242", "fingerprint": "59eb6f11d2422f95682320d9daa3e79fdb076744", "editorCompatibility": "2020.3.0a1", "version": "1.8.21", "source": "registry", "testable": false}, "com.unity.collections@2.5.1": {"name": "com.unity.collections", "displayName": "Collections", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.collections@56bff8827a7e", "fingerprint": "56bff8827a7ef6d44fcee4f36e558a74da89c1a0", "editorCompatibility": "2022.3.11f1", "version": "2.5.1", "source": "registry", "testable": false}, "com.unity.mathematics@1.3.2": {"name": "com.unity.mathematics", "displayName": "Mathematics", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.mathematics@8017b507cc74", "fingerprint": "8017b507cc74bf0a1dd14b18aa860569f807314d", "editorCompatibility": "2021.3.0a1", "version": "1.3.2", "source": "registry", "testable": false}, "com.unity.searcher@4.9.3": {"name": "com.unity.searcher", "displayName": "Searcher", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.searcher@1e17ce91558d", "fingerprint": "1e17ce91558d1d9127554adc03d275f39a7466a2", "editorCompatibility": "2019.1.0a1", "version": "4.9.3", "source": "registry", "testable": false}, "com.unity.modules.terrain@1.0.0": {"name": "com.unity.modules.terrain", "displayName": "Terrain", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrain", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.rendering.light-transport@1.0.1": {"name": "com.unity.rendering.light-transport", "displayName": "Unity Light Transport Library", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.rendering.light-transport@9bd588f963c0", "fingerprint": "9bd588f963c0ca6105f0978f9cfc66444032a804", "editorCompatibility": "2023.3.0b1", "version": "1.0.1", "source": "builtin", "testable": false}, "com.unity.modules.hierarchycore@1.0.0": {"name": "com.unity.modules.hierarchycore", "displayName": "Hierarchy Core", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.hierarchycore", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.ext.nunit@2.0.5": {"name": "com.unity.ext.nunit", "displayName": "Custom NUnit", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.ext.nunit@031a54704bff", "fingerprint": "031a54704bffe39e6a0324909f8eaa4565bdebf2", "editorCompatibility": "2019.4.0a1", "version": "2.0.5", "source": "builtin", "testable": false}, "com.unity.2d.common@9.0.7": {"name": "com.unity.2d.common", "displayName": "2D Common", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.common@bb1fc9b3d81b", "fingerprint": "bb1fc9b3d81b3bb452c6708e8c088fe4224a0369", "editorCompatibility": "2023.1.0a1", "version": "9.0.7", "source": "registry", "testable": false}, "com.unity.modules.tilemap@1.0.0": {"name": "com.unity.modules.tilemap", "displayName": "Tilemap", "resolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.tilemap", "version": "1.0.0", "source": "builtin", "testable": false}, "com.unity.test-framework.performance@3.1.0": {"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "resolvedPath": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed", "fingerprint": "92d1d09a72ed696fa23fd76c675b29d211664b50", "editorCompatibility": "2020.3.0a1", "version": "3.1.0", "source": "registry", "testable": false}}}