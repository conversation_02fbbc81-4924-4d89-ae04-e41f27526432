[{"id": "PAC0000", "type": "UnityEngine.Camera", "method": "main", "areas": ["CPU"], "description": "<b>Camera.main</b> property uses <b>FindGameObjectsWithTag()</b> internally and doesn't cache the result.", "recommendation": "It is advised to cache and use the camera component obtained from Game object instead.", "maximumVersion": "2019.4.8"}, {"id": "PAC0001", "type": "UnityEngine.WebCamTexture", "method": "GetPixels", "areas": ["Memory"], "description": "<b>WebCamTexture.GetPixels()</b> allocates managed memory.", "recommendation": "Use WebCamTexture.GetPixels32() instead."}, {"id": "PAC0002", "type": "UnityEngine.WebCamTexture", "method": "GetPixels32", "areas": ["Memory"], "description": "<b>WebCamTexture.GetPixels32()</b> allocates managed memory if a suitable array is not provided as a parameter.", "recommendation": "Ensure that you pass an array of <b>Color32[]</b> to this API method for it to fill out, to avoid creating a new array every time the method is called."}, {"id": "PAC0003", "type": "UnityEngine.GeometryUtility", "method": "CalculateFrustumPlanes", "areas": ["Memory"], "description": "Some versions of <b>GeometryUtility.CalculateFrustumPlanes()</b> allocate managed memory.", "recommendation": "Ensure that you use the <b>CalculateFrustumPlanes(Matrix4x4 worldToProjectionMatrix, Plane[] planes)</b> version of this API method, in order to be able to pass a pre-allocated Array of Planes."}, {"id": "PAC0004", "type": "UnityEngine.Resources", "method": "FindObjectsOfTypeAll", "areas": ["Memory"], "description": "<b>Resources.FindObjectsOfTypeAll()</b> allocates managed memory.", "recommendation": "Use <b>Resources.FindObjectsOfTypeNonAlloc()</b> instead."}, {"id": "PAC0005", "type": "UnityEngine.Texture2D", "method": "GetPixels", "areas": ["Memory"], "description": "<b>Texture2D.GetPixels()</b> allocates managed memory.", "recommendation": "Use <b>Texture2D.GetRawTextureData()</b> instead. This method returns a NativeArray of pixel data, and so does not allocate managed memory."}, {"id": "PAC0006", "type": "UnityEngine.Texture2D", "method": "GetPixels32", "areas": ["Memory"], "description": "<b>Texture2D.GetPixels32()</b> allocates managed memory.", "recommendation": "Use <b>Texture2D.GetRawTextureData()</b> instead. This method returns a NativeArray of pixel data, and so does not allocate managed memory."}, {"id": "PAC0007", "type": "UnityEngine.Rigidbody", "method": "SweepTestAll", "areas": ["Memory"], "description": "<b>Rigidbody.SweepTestAll()</b> allocates managed memory.", "recommendation": "Use <b>Rigidbody.SweepTestNonAlloc()</b> instead."}, {"id": "PAC0008", "type": "UnityEngine.Physics", "method": "RaycastAll", "areas": ["Memory"], "description": "<b>Physics.RaycastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics.RaycastNonAlloc()</b> instead."}, {"id": "PAC0009", "type": "UnityEngine.Physics", "method": "CapsuleCastAll", "areas": ["Memory"], "description": "<b>Physics.CapsuleCastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics.CapsuleCastNonAlloc()</b> instead."}, {"id": "PAC0010", "type": "UnityEngine.Physics", "method": "SphereCastAll", "areas": ["Memory"], "description": "<b>Physics.SphereCastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics.SphereCastNonAlloc()</b> instead."}, {"id": "PAC0011", "type": "UnityEngine.Physics", "method": "BoxCastAll", "areas": ["Memory"], "description": "<b>Physics.BoxCastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics.BoxCastNonAlloc()</b> instead."}, {"id": "PAC0012", "type": "UnityEngine.Physics", "method": "OverlapCapsule", "areas": ["Memory"], "description": "<b>Physics.OverlapCapsule()</b> allocates managed memory.", "recommendation": "Use <b>Physics.OverlapCapsuleNonAlloc()</b> instead."}, {"id": "PAC0013", "type": "UnityEngine.Physics", "method": "OverlapSphere", "areas": ["Memory"], "description": "<b>Physics.OverlapSphere()</b> allocates managed memory.", "recommendation": "Use <b>Physics.OverlapSphereNonAlloc()</b> instead."}, {"id": "PAC0014", "type": "UnityEngine.Physics", "method": "OverlapBox", "areas": ["Memory"], "description": "<b>Physics.OverlapBox()</b> allocates managed memory.", "recommendation": "Use <b>Physics.OverlapBoxNonAlloc()</b> instead."}, {"id": "PAC0015", "type": "UnityEngine.Physics2D", "method": "LinecastAll", "areas": ["Memory"], "description": "<b>Physics2D.LinecastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.LinecastNonAlloc()</b> instead."}, {"id": "PAC0016", "type": "UnityEngine.Physics2D", "method": "RaycastAll", "areas": ["Memory"], "description": "<b>Physics2D.RaycastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.RaycastNonAlloc()</b> instead."}, {"id": "PAC0017", "type": "UnityEngine.Physics2D", "method": "CircleCastAll", "areas": ["Memory"], "description": "<b>Physics2D.CircleCastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.CircleCastNonAlloc()</b> instead."}, {"id": "PAC0018", "type": "UnityEngine.Physics2D", "method": "BoxCastAll", "areas": ["Memory"], "description": "<b>Physics2D.BoxCastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.BoxCastNonAlloc()</b> instead."}, {"id": "PAC0019", "type": "UnityEngine.Physics2D", "method": "CapsuleCastAll", "areas": ["Memory"], "description": "<b>Physics2D.CapsuleCastAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.CapsuleCastNonAlloc()</b> instead."}, {"id": "PAC0020", "type": "UnityEngine.Physics2D", "method": "GetRayIntersectionAll", "areas": ["Memory"], "description": "<b>Physics2D.GetRayIntersectionAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.GetRayIntersectionNonAlloc()</b> instead."}, {"id": "PAC0021", "type": "UnityEngine.Physics2D", "method": "OverlapPointAll", "areas": ["Memory"], "description": "<b>Physics2D.OverlapPointAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.OverlapPointNonAlloc()</b> instead."}, {"id": "PAC0022", "type": "UnityEngine.Physics2D", "method": "OverlapCircleAll", "areas": ["Memory"], "description": "<b>Physics2D.OverlapCircleAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.OverlapCircleNonAlloc()</b> instead."}, {"id": "PAC0023", "type": "UnityEngine.Physics2D", "method": "OverlapBoxAll", "areas": ["Memory"], "description": "<b>Physics2D.OverlapBoxAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.OverlapBoxNonAlloc()</b> instead."}, {"id": "PAC0024", "type": "UnityEngine.Physics2D", "method": "OverlapAreaAll", "areas": ["Memory"], "description": "<b>Physics2D.OverlapAreaAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.OverlapAreaNonAlloc()</b> instead."}, {"id": "PAC0025", "type": "UnityEngine.Physics2D", "method": "OverlapCapsuleAll", "areas": ["Memory"], "description": "<b>Physics2D.OverlapCapsuleAll()</b> allocates managed memory.", "recommendation": "Use <b>Physics2D.OverlapCapsuleNonAlloc()</b> instead."}, {"id": "PAC0026", "type": "UnityEngine.Component", "method": "GetComponentsInChildren", "areas": ["Memory"], "description": "<b>Component.GetComponentsInChildren()</b> allocates managed memory.", "recommendation": "Ensure you are using one of the versions of <b>GameObject.GetComponentsInChildren()</b> which accepts a List<T> as a parameter and populates it with the components it finds."}, {"id": "PAC0027", "type": "UnityEngine.Component", "method": "GetComponentsInParent", "areas": ["Memory"], "description": "<b>Component.GetComponentsInParent()</b> allocates managed memory.", "recommendation": "Ensure you are using one of the versions of <b>GameObject.GetComponentsInParent()</b> which accepts a List<T> as a parameter and populates it with the components it finds."}, {"id": "PAC0028", "type": "UnityEngine.GameObject", "method": "GetComponentsInChildren", "areas": ["Memory"], "description": "Some versions of <b>GameObject.GetComponentsInChildren()</b> allocate managed memory.", "recommendation": "Ensure you are using one of the versions of <b>GameObject.GetComponentsInChildren()</b> which accepts a List<T> as a parameter and populates it with the components it finds."}, {"id": "PAC0029", "type": "UnityEngine.GameObject", "method": "GetComponentsInParent", "areas": ["Memory"], "description": "Some versions of <b>GameObject.GetComponentsInParent()</b> allocate managed memory.", "recommendation": "Ensure you are using one of the versions of <b>GameObject.GetComponentsInParent()</b> which accepts a List<T> as a parameter and populates it with the components it finds."}, {"id": "PAC0030", "type": "UnityEngine.Collider", "method": "OnTriggerStay", "areas": ["CPU"], "description": "<b>OnTriggerStay()</b> methods can detrimentally affect performance if they perform a lot of processing or if there are many Colliders which implement this method.", "recommendation": "Profile CPU performance to look for bottlenecks, examine the contents of all <b>OnTriggerStay()</b> methods, and consider refactoring code to not use them."}, {"id": "PAC0031", "type": "UnityEngine.MonoBehaviour", "method": "OnTriggerStay", "areas": ["CPU"], "description": "<b>OnTriggerStay()</b> methods can detrimentally affect performance if they perform a lot of processing or if there are many MonoBehaviours which implement this method.", "recommendation": "Profile CPU performance to look for bottlenecks, examine the contents of all <b>OnTriggerStay()</b> methods, and consider refactoring code to not use them."}, {"id": "PAC0032", "type": "UnityEngine.Collider2D", "method": "OnTriggerStay2D", "areas": ["CPU"], "description": "<b>OnTriggerStay2D()</b> methods can detrimentally affect performance if they perform a lot of processing or if there are many Collider2Ds which implement this method.", "recommendation": "Profile CPU performance to look for bottlenecks, examine the contents of all <b>OnTriggerStay2D()</b> methods, and consider refactoring code to not use them."}, {"id": "PAC0033", "type": "UnityEngine.MonoBehaviour", "method": "OnTriggerStay2D", "areas": ["CPU"], "description": "<b>OnTriggerStay2D()</b> methods can detrimentally affect performance if they perform a lot of processing or if there are many MonoBehaviours which implement this method.", "recommendation": "Profile CPU performance to look for bottlenecks, examine the contents of all <b>OnTriggerStay2D()</b> methods, and consider refactoring code to not use them."}, {"id": "PAC0034", "type": "UnityEngine.Collider", "method": "OnCollisionStay", "areas": ["CPU"], "description": "<b>OnCollisionStay()</b> methods can detrimentally affect performance if they perform a lot of processing or if there are many Colliders which implement this method.", "recommendation": "Profile CPU performance to look for bottlenecks, examine the contents of all <b>OnCollisionStay()</b> methods, and consider refactoring code to not use them."}, {"id": "PAC0035", "type": "UnityEngine.MonoBehaviour", "method": "OnCollisionStay", "areas": ["CPU"], "description": "<b>OnCollisionStay()</b> methods can detrimentally affect performance if they perform a lot of processing or if there are many MonoBehaviours which implement this method.", "recommendation": "Profile CPU performance to look for bottlenecks, examine the contents of all <b>OnCollisionStay()</b> methods, and consider refactoring code to not use them."}, {"id": "PAC0036", "type": "UnityEngine.Rigidbody", "method": "OnCollisionStay", "areas": ["CPU"], "description": "<b>OnCollisionStay()</b> methods can detrimentally affect performance if they perform a lot of processing or if there are many RigidBodies which implement this method.", "recommendation": "Profile CPU performance to look for bottlenecks, examine the contents of all <b>OnCollisionStay()</b> methods, and consider refactoring code to not use them."}, {"id": "PAC0037", "type": "UnityEngine.ImageConversion", "method": "LoadImage", "areas": ["Memory"], "description": "<b>ImageConversion.LoadImage()</b> defaults to maintaining a CPU-accessible copy of the image. This is a waste of memory if not needed.", "recommendation": "If a CPU-accessible copy of the texture is not required, ensure that the markNonReadable flag passed into the method is set to true."}, {"id": "PAC0039", "type": "UnityEngine.Renderer", "method": "material", "areas": ["GPU"], "description": "The <b>Renderer.material</b> property creates a unique copy of the Renderer's material. This breaks draw call batching and results in a higher number of draw calls, impacting rendering performance.", "recommendation": "If possible, use <b>Renderer.sharedMaterial</b> instead."}, {"id": "PAC0051", "type": "UnityEngine.ComputeBuffer", "method": "GetData", "areas": ["CPU"], "description": "<b>ComputeBuffer.GetData()</b> stalls the CPU until the GPU has finished accessing the buffer. This can lead to significant CPU performance problems.", "recommendation": "Avoid reading back from ComputeBuffers if it is at all possible. If it's unavoidable, profile your project carefully and regularly to monitor the performance impact."}, {"id": "PAC0052", "type": "UnityEngine.Texture2D", "method": "SetPixels", "areas": ["CPU"], "description": "<b>Texture2D.SetPixels()</b> is slower than <b>Texture2D.SetPixels32()</b>.", "recommendation": "Use <b>Texture2D.SetPixels32()</b>, <b>Texture2D.GetRawTextureData()</b>, or <b>Texture2D.Apply()</b> instead."}, {"id": "PAC0053", "type": "UnityEngine.Texture3D", "method": "SetPixels", "areas": ["CPU"], "description": "<b>Texture3D.SetPixels()</b> is slower than <b>Texture3D.SetPixels32()</b>.", "recommendation": "Use <b>Texture3D.SetPixels32()</b> instead."}, {"id": "PAC0054", "type": "UnityEngine.Texture2DArray", "method": "SetPixels", "areas": ["CPU"], "description": "<b>Texture2DArray.SetPixels()</b> is slower than <b>Texture2DArray.SetPixels32()</b>.", "recommendation": "Use <b>Texture2DArray.SetPixels32()</b> instead."}, {"id": "PAC0055", "type": "UnityEngine.CubemapArray", "method": "SetPixels", "areas": ["CPU"], "description": "<b>CubemapArray.SetPixels()</b> is slower than <b>CubemapArray.SetPixels32()</b>.", "recommendation": "Use <b>CubemapArray.SetPixels32()</b> instead."}, {"id": "PAC0056", "type": "UnityEngine.GameObject", "method": "SendMessage", "areas": ["CPU"], "description": "<b>GameObject.SendMessage()</b> is a very slow and CPU-intensive method.", "recommendation": "Implement a custom system to replace SendMessage - get the components you want to send messages to and call methods directly on them."}, {"id": "PAC0057", "type": "UnityEngine.Component", "method": "SendMessage", "areas": ["CPU"], "description": "<b>Component.SendMessage()</b> is a very slow and CPU-intensive method.", "recommendation": "Implement a custom system to replace SendMessage - get the components you want to send messages to and call methods directly on them."}, {"id": "PAC0058", "type": "UnityEngine.MonoBehaviour", "method": "OnGUI", "areas": ["CPU"], "description": "<b>OnGUI()</b> is used by the legacy Immediate Mode GUI (IMGUI), which is extremely CPU-intensive. If a single OnGUI() method is present in a project's code, IMGUI will initialize and consume CPU time.", "recommendation": "Remove all <b>OnGUI()</b> methods from the project code."}, {"id": "PAC0059", "type": "UnityEngine.AI.Nav<PERSON>", "method": "corners", "areas": ["Memory"], "description": "The property <b>AI.NavMeshPath.corners</b> allocates managed memory.", "recommendation": "Use <b>AI.NavMeshPath.GetCornersNonAlloc()</b> instead."}, {"id": "PAC0060", "type": "UnityEngine.Animator", "method": "parameters", "areas": ["Memory"], "description": "The property Animator.parameters allocates managed memory.", "recommendation": "Use <b>Animator.GetParameter()</b> instead."}, {"id": "PAC0061", "type": "UnityEngine.Animations.ParentConstraint", "method": "translationOffsets", "areas": ["Memory"], "description": "The property <b>Animations.ParentConstraint.translationOffsets</b> allocates managed memory.", "recommendation": "Use <b>Animations.ParentConstraint.GetTranslationOffset()</b> instead."}, {"id": "PAC0062", "type": "UnityEngine.Animations.ParentConstraint", "method": "rotationOffsets", "areas": ["Memory"], "description": "The property <b>Animations.ParentConstraint.rotationOffsets</b> allocates managed memory.", "recommendation": "Use <b>Animations.ParentConstraint.GetRotationOffset()</b> instead."}, {"id": "PAC0063", "type": "UnityEngine.AnimationCurve", "method": "keys", "areas": ["Memory"], "description": "The property <b>AnimationCurve.keys</b> allocates managed memory.", "recommendation": "Use <b>AnimationCurve.AddKey()</b>, <b>AnimationCurve.MoveKey()</b> or <b>AnimationCurve.RemoveKey()</b> instead."}, {"id": "PAC0066", "type": "UnityEngine.Camera", "method": "allCameras", "areas": ["Memory"], "description": "The property <b>Camera.allCameras</b> allocates managed memory.", "recommendation": "Use <b>Camera.GetAllCameras()</b> instead."}, {"id": "PAC0067", "type": "UnityEngine.Mesh", "method": "boneWeights", "areas": ["Memory"], "description": "The property <b>Me<PERSON>.boneWeights</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetAllBoneWeights()</b> instead. This method returns a NativeArray of BoneWeight1, and so does not allocate managed memory."}, {"id": "PAC0068", "type": "UnityEngine.Mesh", "method": "bindposes", "areas": ["Memory"], "description": "The property <b>Mesh.bindposes</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetBindposes()</b> instead."}, {"id": "PAC0069", "type": "UnityEngine.Mesh", "method": "vertices", "areas": ["Memory"], "description": "The property <b>Mesh.vertices</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetVertices()</b> instead."}, {"id": "PAC0070", "type": "UnityEngine.Mesh", "method": "normals", "areas": ["Memory"], "description": "The property <b>Mesh.normals</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetNormals()</b> instead."}, {"id": "PAC0071", "type": "UnityEngine.Mesh", "method": "tangents", "areas": ["Memory"], "description": "The property <b>Mesh.tangents</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetTangents()</b> instead."}, {"id": "PAC0072", "type": "UnityEngine.Mesh", "method": "uv", "areas": ["Memory"], "description": "The property <b>Mesh.uv</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0073", "type": "UnityEngine.Mesh", "method": "uv1", "areas": ["Memory"], "description": "The property <b>Mesh.uv1</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0074", "type": "UnityEngine.Mesh", "method": "uv2", "areas": ["Memory"], "description": "The property <b>Mesh.uv2</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0075", "type": "UnityEngine.Mesh", "method": "uv3", "areas": ["Memory"], "description": "The property <b>Mesh.uv3</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0076", "type": "UnityEngine.Mesh", "method": "uv4", "areas": ["Memory"], "description": "The property <b>Mesh.uv4</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0077", "type": "UnityEngine.Mesh", "method": "uv5", "areas": ["Memory"], "description": "The property <b>Mesh.uv5</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0078", "type": "UnityEngine.Mesh", "method": "uv6", "areas": ["Memory"], "description": "The property <b>Mesh.uv6</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0079", "type": "UnityEngine.Mesh", "method": "uv7", "areas": ["Memory"], "description": "The property <b>Mesh.uv7</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0080", "type": "UnityEngine.Mesh", "method": "uv8", "areas": ["Memory"], "description": "The property <b>Mesh.uv8</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetUVs()</b> instead."}, {"id": "PAC0081", "type": "UnityEngine.Mesh", "method": "colors", "areas": ["Memory"], "description": "The property <b>Mesh.colors</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetColors()</b> instead."}, {"id": "PAC0082", "type": "UnityEngine.Mesh", "method": "colors32", "areas": ["Memory"], "description": "The property <b>Mesh.colors32</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetColors()</b> instead."}, {"id": "PAC0083", "type": "UnityEngine.Mesh", "method": "triangles", "areas": ["Memory"], "description": "The property <b>Mesh.triangles</b> allocates managed memory.", "recommendation": "Use <b>Mesh.GetTriangles()</b> instead."}, {"id": "PAC0084", "type": "UnityEngine.Renderer", "method": "materials", "areas": ["Memory"], "description": "The property <b>Renderer.materials</b> allocates managed memory.", "recommendation": "Use <b>Renderer.GetMaterials()</b> instead."}, {"id": "PAC0085", "type": "UnityEngine.Renderer", "method": "sharedMaterials", "areas": ["Memory"], "description": "The property <b>Renderer.sharedMaterials</b> allocates managed memory.", "recommendation": "Use <b>Renderer.GetSharedMaterials()</b> instead."}, {"id": "PAC0094", "type": "UnityEngine.Input", "method": "touches", "areas": ["Memory"], "description": "The property <b>Input.touches</b> allocates managed memory.", "recommendation": "Use <b>Input.GetTouch()</b> instead."}, {"id": "PAC0095", "type": "UnityEngine.Input", "method": "accelerationEvents", "areas": ["Memory"], "description": "The property <b>Input.accelerationEvents</b> allocates managed memory.", "recommendation": "Use <b>Input.GetAccelerationEvent()</b> instead."}, {"id": "PAC0096", "type": "UnityEngine.iOS.NotificationServices", "method": "localNotifications", "areas": ["Memory"], "description": "The property <b>iOS.NotificationServices.localNotifications</b> allocates managed memory.", "recommendation": "Use <b>iOS.NotificationServices.GetLocalNotification()</b> instead.", "maximumVersion": "2021.3", "platforms": ["iOS"]}, {"id": "PAC0097", "type": "UnityEngine.iOS.NotificationServices", "method": "remoteNotifications", "areas": ["Memory"], "description": "The property <b>iOS.NotificationServices.remoteNotifications</b> allocates managed memory.", "recommendation": "Use <b>iOS.NotificationServices.GetRemoteNotification()</b> instead.", "maximumVersion": "2021.3", "platforms": ["iOS"]}, {"id": "PAC0100", "type": "UnityEngine.GUISkin", "method": "customStyles", "areas": ["Memory"], "description": "The property <b>GUISkin.customStyles</b> allocates managed memory.", "recommendation": "Use <b>GUISkin.GetStyle()</b> or <b>GUISkin.FindStyle()</b> instead."}, {"id": "PAC0103", "type": "UnityEngine.Collision", "method": "contacts", "areas": ["Memory"], "description": "The property <b>Collision.contacts</b> allocates managed memory.", "recommendation": "Use <b>Collision.GetContacts()</b> instead."}, {"id": "PAC0104", "type": "UnityEngine.Collision2D", "method": "contacts", "areas": ["Memory"], "description": "The property <b>Collision2D.contacts</b> allocates managed memory.", "recommendation": "Use <b>Collision2D.GetContacts()</b> instead."}, {"id": "PAC0110", "type": "UnityEngine.TerrainData", "method": "treeInstances", "areas": ["Memory"], "description": "The property <b>TerrainData.treeInstances</b> allocates managed memory.", "recommendation": "Use <b>TerrainData.GetTreeInstance()</b> instead."}, {"id": "PAC0111", "type": "UnityEngine.TerrainData", "method": "alphamapTextures", "areas": ["Memory"], "description": "The property <b>TerrainData.alphamapTextures</b> allocates managed memory.", "recommendation": "Use <b>TerrainData.GetAlphamapTexture()</b> instead."}, {"id": "PAC0112", "type": "UnityEngine.Font", "method": "characterInfo", "areas": ["Memory"], "description": "The property <b>Font.characterInfo</b> allocates managed memory.", "recommendation": "Use <b>Font.GetCharacterInfo()</b> instead."}, {"id": "PAC0115", "type": "UnityEngine.Animator", "method": "GetCurrentAnimatorClipInfo", "areas": ["Memory"], "description": "<b>Animator.GetCurrentAnimatorClipInfo()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0116", "type": "UnityEngine.Animator", "method": "GetBehaviours", "areas": ["Memory"], "description": "<b>Animator.GetBehaviours()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0117", "type": "UnityEngine.AssetBundle", "method": "LoadAssetWithSubAssets", "areas": ["Memory"], "description": "<b>AssetBundle.LoadAssetWithSubAssets()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0118", "type": "UnityEngine.AssetBundle", "method": "LoadAllAssets", "areas": ["Memory"], "description": "<b>AssetBundle.LoadAllAssets()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0119", "type": "UnityEngine.AssetBundleManifest", "method": "GetAllAssetBundles", "areas": ["Memory"], "description": "<b>AssetBundleManifest.GetAllAssetBundles()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0120", "type": "UnityEngine.Resources", "method": "LoadAll", "areas": ["Memory"], "description": "<b>Resources.LoadAll()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0121", "type": "UnityEngine.Texture2D", "method": "PackTextures", "areas": ["Memory"], "description": "<b>Texture2D.PackTextures()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0122", "type": "UnityEngine.Cubemap", "method": "GetPixels", "areas": ["Memory"], "description": "<b>Cubemap.GetPixels()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0123", "type": "UnityEngine.Texture3D", "method": "GetPixels", "areas": ["Memory"], "description": "<b>Texture3D.GetPixels()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0124", "type": "UnityEngine.Texture3D", "method": "GetPixels32", "areas": ["Memory"], "description": "<b>Texture3D.GetPixels32()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0125", "type": "UnityEngine.Texture2DArray", "method": "GetPixels", "areas": ["Memory"], "description": "<b>Texture2DArray.GetPixels()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0126", "type": "UnityEngine.Texture2DArray", "method": "GetPixels32", "areas": ["Memory"], "description": "<b>Texture2DArray.GetPixels32()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0127", "type": "UnityEngine.CubemapArray", "method": "GetPixels", "areas": ["Memory"], "description": "<b>CubemapArray.GetPixels()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0128", "type": "UnityEngine.CubemapArray", "method": "GetPixels32", "areas": ["Memory"], "description": "<b>CubemapArray.GetPixels32()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0129", "type": "UnityEngine.Object", "method": "FindObjectsOfType", "areas": ["CPU", "Memory"], "description": "<b>Object.FindObjectsOfType()</b> allocates managed memory and can be slow.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0130", "type": "UnityEngine.Windows.Crypto", "method": "ComputeMD5Hash", "areas": ["Memory"], "description": "<b>Windows.Crypto.ComputeMD5Hash()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0131", "type": "UnityEngine.Windows.File", "method": "ReadAllBytes", "areas": ["Memory"], "description": "<b>Windows.File.ReadAllBytes()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0132", "type": "UnityEngine.ImageConversion", "method": "EncodeToJPG", "areas": ["Memory"], "description": "<b>ImageConversion.EncodeToJPG()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0133", "type": "UnityEngine.ImageConversion", "method": "EncodeToEXR", "areas": ["Memory"], "description": "<b>ImageConversion.EncodeToEXR()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0134", "type": "UnityEngine.ImageConversion", "method": "EncodeToTGA", "areas": ["Memory"], "description": "<b>ImageConversion.EncodeToTGA()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0135", "type": "UnityEngine.ImageConversion", "method": "EncodeToPNG", "areas": ["Memory"], "description": "<b>ImageConversion.EncodeToPNG()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0136", "type": "UnityEngine.U2D.SpriteShapeUtility", "method": "Generate", "areas": ["Memory"], "description": "<b>U2D.SpriteShapeUtility.Generate()</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used.", "minimumVersion": "2019.3"}, {"id": "PAC0138", "type": "UnityEngine.AI.NavMeshTriangulation", "method": "layers", "areas": ["Memory"], "description": "The property <b>AI.NavMeshTriangulation.layers</b> allocates managed memory.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0139", "type": "UnityEngine.AnimationClip", "method": "events", "areas": ["Memory"], "description": "The property <b>AnimationClip.events</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0140", "type": "UnityEngine.AnimatorOverrideController", "method": "animationClips", "areas": ["Memory"], "description": "The property <b>AnimatorOverrideController.animationClips</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0141", "type": "UnityEngine.HumanTrait", "method": "Muscle<PERSON>ame", "areas": ["Memory"], "description": "The property <b>HumanTrait.MuscleName</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0142", "type": "UnityEngine.HumanTrait", "method": "Bone<PERSON>ame", "areas": ["Memory"], "description": "The property <b>HumanTrait.BoneName</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0143", "type": "UnityEngine.RuntimeAnimatorController", "method": "animationClips", "areas": ["Memory"], "description": "The property <b>RuntimeAnimatorController.animationClips</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0144", "type": "UnityEngine.AssetBundleRequest", "method": "allAssets", "areas": ["Memory"], "description": "The property <b>AssetBundleRequest.allAssets</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0145", "type": "UnityEngine.Microphone", "method": "devices", "areas": ["Memory"], "description": "The property <b>Microphone.devices</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0146", "type": "UnityEngine.WebCamDevice", "method": "availableResolutions", "areas": ["Memory"], "description": "The property <b>WebCamDevice.availableResolutions</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0147", "type": "UnityEngine.WebCamTexture", "method": "devices", "areas": ["Memory"], "description": "The property <b>WebCamTexture.devices</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0148", "type": "UnityEngine.Cloth", "method": "vertices", "areas": ["Memory"], "description": "The property <b>Cloth.vertices</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0149", "type": "UnityEngine.Cloth", "method": "normals", "areas": ["Memory"], "description": "The property <b>Cloth.normals</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0150", "type": "UnityEngine.Cloth", "method": "coefficients", "areas": ["Memory"], "description": "The property <b>Cloth.coefficients</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0151", "type": "UnityEngine.Cloth", "method": "capsuleColliders", "areas": ["Memory"], "description": "The property <b>Cloth.capsuleColliders</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0152", "type": "UnityEngine.Cloth", "method": "sphereColliders", "areas": ["Memory"], "description": "The property <b><PERSON>lot<PERSON>.sphereColliders</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0153", "type": "UnityEngine.Camera", "method": "layerCullDistances", "areas": ["Memory"], "description": "The property <b>Camera.layerCullDistances</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0154", "type": "UnityEngine.CrashReport", "method": "reports", "areas": ["Memory"], "description": "The property CrashReport.reports allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0155", "type": "UnityEngine.Gradient", "method": "colorKeys", "areas": ["Memory"], "description": "The property <b>Gradient.colorKeys</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0156", "type": "UnityEngine.Gradient", "method": "alphaKeys", "areas": ["Memory"], "description": "The property <b>Gradient.alphaKeys</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0157", "type": "UnityEngine.Screen", "method": "resolutions", "areas": ["Memory"], "description": "The property <b>Screen.resolutions</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0158", "type": "UnityEngine.LightmapSettings", "method": "lightmaps", "areas": ["Memory"], "description": "The property <b>LightmapSettings.lightmaps</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0159", "type": "UnityEngine.LightProbes", "method": "positions", "areas": ["Memory"], "description": "The property <b>LightProbes.positions</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0160", "type": "UnityEngine.LightProbes", "method": "bakedProbes", "areas": ["Memory"], "description": "The property <b>LightProbes.bakedProbes</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0161", "type": "UnityEngine.LightProbes", "method": "coefficients", "areas": ["Memory"], "description": "The property <b>LightProbes.coefficients</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0162", "type": "UnityEngine.QualitySettings", "method": "names", "areas": ["Memory"], "description": "The property <b>QualitySettings.names</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0163", "type": "UnityEngine.Material", "method": "shaderKeywords", "areas": ["Memory"], "description": "The property <b>Material.shaderKeywords</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0164", "type": "UnityEngine.Light", "method": "layerShadowCullDistances", "areas": ["Memory"], "description": "The property <b>Light.layerShadowCullDistances</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0165", "type": "UnityEngine.Rendering.RenderTargetBinding", "method": "colorRenderTargets", "areas": ["Memory"], "description": "The property <b>Rendering.RenderTargetBinding.colorRenderTargets</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0166", "type": "UnityEngine.Rendering.RenderTargetBinding", "method": "colorLoadActions", "areas": ["Memory"], "description": "The property <b>Rendering.RenderTargetBinding.colorLoadActions</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0167", "type": "UnityEngine.Rendering.RenderTargetBinding", "method": "colorStoreActions", "areas": ["Memory"], "description": "The property <b>Rendering.RenderTargetBinding.colorStoreActions</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0168", "type": "UnityEngine.SkinnedMeshRenderer", "method": "bones", "areas": ["Memory"], "description": "The property <b>SkinnedMeshRenderer.bones</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0169", "type": "UnityEngine.LightProbeGroup", "method": "probePositions", "areas": ["Memory"], "description": "The property <b>LightProbeGroup.probePositions</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0170", "type": "UnityEngine.Network", "method": "connections", "areas": ["Memory"], "description": "The property <b>Network.connections</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used.", "maximumVersion": "2018.4"}, {"id": "PAC0171", "type": "UnityEngine.HostData", "method": "ip", "areas": ["Memory"], "description": "The property <b>HostData.ip</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used.", "maximumVersion": "2018.4"}, {"id": "PAC0172", "type": "UnityEngine.SortingLayer", "method": "layers", "areas": ["Memory"], "description": "The property <b>SortingLayer.layers</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0173", "type": "UnityEngine.TextAsset", "method": "bytes", "areas": ["Memory"], "description": "The property <b>TextAsset.bytes</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0175", "type": "UnityEngine.iOS.NotificationServices", "method": "deviceToken", "areas": ["Memory"], "description": "The property <b>iOS.NotificationServices.deviceToken</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used.", "maximumVersion": "2021.3", "platforms": ["iOS"]}, {"id": "PAC0176", "type": "UnityEngine.iOS.NotificationServices", "method": "scheduledLocalNotifications", "areas": ["Memory"], "description": "The property <b>iOS.NotificationServices.scheduledLocalNotifications</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used.", "maximumVersion": "2021.3", "platforms": ["iOS"]}, {"id": "PAC0177", "type": "UnityEngine.Sprite", "method": "vertices", "areas": ["Memory"], "description": "The property <b>Sprite.vertices</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0178", "type": "UnityEngine.Sprite", "method": "triangles", "areas": ["Memory"], "description": "The property <b>Sprite.triangles</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0179", "type": "UnityEngine.Sprite", "method": "uv", "areas": ["Memory"], "description": "The property <b>Sprite.uv</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0180", "type": "UnityEngine.SocialPlatforms.ILocalUser", "method": "friends", "areas": ["Memory"], "description": "The property <b>SocialPlatforms.ILocalUser.friends</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0181", "type": "UnityEngine.SocialPlatforms.ILeaderboard", "method": "scores", "areas": ["Memory"], "description": "The property <b>SocialPlatforms.ILeaderboard.scores</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0182", "type": "UnityEngine.GUIStyleState", "method": "scaledBackgrounds", "areas": ["Memory"], "description": "The property <b>GUIStyleState.scaledBackgrounds</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0183", "type": "UnityEngine.EdgeCollider2D", "method": "points", "areas": ["Memory"], "description": "The property <b>EdgeCollider2D.points</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0184", "type": "UnityEngine.PolygonCollider2D", "method": "points", "areas": ["Memory"], "description": "The property <b>PolygonCollider2D.points</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0185", "type": "UnityEngine.Terrain", "method": "activeTerrains", "areas": ["Memory"], "description": "The property <b>Terrain.activeTerrains</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0186", "type": "UnityEngine.TerrainData", "method": "detailPrototypes", "areas": ["Memory"], "description": "The property <b>TerrainData.detailPrototypes</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0187", "type": "UnityEngine.TerrainData", "method": "treePrototypes", "areas": ["Memory"], "description": "The property <b>TerrainData.treePrototypes</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0188", "type": "UnityEngine.TerrainData", "method": "splatPrototypes", "areas": ["Memory"], "description": "The property <b>TerrainData.splatPrototypes</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0189", "type": "UnityEngine.TerrainData", "method": "terrainLayers", "areas": ["Memory"], "description": "The property <b>TerrainData.terrainLayers</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0190", "type": "UnityEngine.Tilemaps.TileAnimationData", "method": "animatedSprites", "areas": ["Memory"], "description": "The property <b>Tilemaps.TileAnimationData.animatedSprites</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0191", "type": "UnityEngine.UIElements.UxmlAttributeDescription", "method": "obsoleteNames", "areas": ["Memory"], "description": "The property <b>UIElements.UxmlAttributeDescription.obsoleteNames</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used.", "minimumVersion": "2019.1"}, {"id": "PAC0200", "type": "UnityEngine.Networking.IMultipartFormSection", "method": "sectionData", "areas": ["Memory"], "description": "The property <b>Networking.IMultipartFormSection.sectionData</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0201", "type": "UnityEngine.Networking.MultipartFormDataSection", "method": "sectionData", "areas": ["Memory"], "description": "The property <b>Networking.MultipartFormDataSection.sectionData</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0202", "type": "UnityEngine.Networking.MultipartFormFileSection", "method": "sectionData", "areas": ["Memory"], "description": "The property <b>Networking.MultipartFormFileSection.sectionData</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0203", "type": "UnityEngine.WWWForm", "method": "data", "areas": ["Memory"], "description": "The property <b>WWWForm.data</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0204", "type": "UnityEngine.Networking.DownloadHandler", "method": "data", "areas": ["Memory"], "description": "The property Networking.DownloadHandler.data allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0208", "type": "UnityEngine.Networking.UploadHandler", "method": "data", "areas": ["Memory"], "description": "The property <b>Networking.UploadHandler.data</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0215", "type": "UnityEngine.Networking.NetworkMigrationManager", "method": "peers", "areas": ["Memory"], "description": "The property <b>Networking.NetworkMigrationManager.peers</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used.", "maximumVersion": "2018.4"}, {"id": "PAC0216", "type": "UnityEngine.Networking.NetworkServerSimple", "method": "messageBuffer", "areas": ["Memory"], "description": "The property <b>Networking.NetworkServerSimple.messageBuffer</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used.", "maximumVersion": "2018.4"}, {"id": "PAC0217", "type": "UnityEngine.WWW", "method": "bytes", "areas": ["Memory"], "description": "The property <b>WWW.bytes</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0219", "type": "UnityEngine.XR.XRSettings", "method": "supportedDevices", "areas": ["Memory"], "description": "The property <b>XR.XRSettings.supportedDevices</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0220", "type": "UnityEngine.TestTools.Constraints.AllocatingGCMemoryConstraint", "method": "Arguments", "areas": ["Memory"], "description": "The property <b>TestTools.Constraints.AllocatingGCMemoryConstraint.Arguments</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0221", "type": "UnityEngine.TestTools.UnityPlatformAttribute", "method": "include", "areas": ["Memory"], "description": "The property <b>TestTools.UnityPlatformAttribute.include</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0222", "type": "UnityEngine.TestTools.UnityPlatformAttribute", "method": "exclude", "areas": ["Memory"], "description": "The property <b>TestTools.UnityPlatformAttribute.exclude</b> allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC0223", "type": "UnityEngine.GameObject", "method": "tag", "areas": ["Memory"], "description": "The <b>GameObject.tag</b> property allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Prefer using <b>GameObject.CompareTag()</b> instead, as this does not result in managed allocations."}, {"id": "PAC0224", "type": "UnityEngine.Object", "method": "Instantiate", "areas": ["CPU", "Memory"], "description": "Creating Objects at runtime by calling <b>Object.Instantiate</b> can take a significant amount of CPU time and allocates managed memory.", "recommendation": "Try to avoid calling <b>Object.Instantiate</b> in frequently-updated code. Consider implementing an Object Pool. The <b>ObjectPool</b> class is available from Unity 2021.1 as a convenience."}, {"id": "PAC0225", "type": "UnityEngine.GameObject", "method": "AddComponent", "areas": ["CPU", "Memory"], "description": "Adding components to GameObjects at runtime can take a significant amount of CPU time and allocates managed memory.", "recommendation": "Try to avoid adding or removing components in frequently-updated code. Prefer instantiating GameObjects from Prefabs will all the necessary components instead."}, {"id": "PAC0227", "type": "UnityEngine.Shader", "method": "WarmupAllShaders", "areas": ["CPU"], "defaultSeverity": 3, "description": "<b>WarmupAllShaders</b> does not work properly on Metal, Vulkan or DX12. This might result in unexpected CPU spikes due to shader compilation.", "recommendation": "Implement a shader pre-warming mechanism which renders a small triangle for each combination of vertex format and shader used at runtime. One way to achieve this is with the <b>UnityEngine.Experimental.Rendering.ShaderWarmup</b> API."}, {"id": "PAC0228", "type": "UnityEngine.ShaderVariantCollection", "method": "WarmUp", "areas": ["CPU"], "defaultSeverity": 3, "description": "<b>WarmUp</b> does not work properly on Metal, Vulkan or DX12. This might result in unexpected CPU spikes due to shader compilation.", "recommendation": "Implement a shader pre-warming mechanism which renders a small triangle for each combination of vertex format and shader variant used at runtime. One way to achieve this is with the <b>UnityEngine.Experimental.Rendering.ShaderWarmup</b> API."}, {"id": "PAC0229", "type": "UnityEngine.Component", "method": "tag", "areas": ["Memory"], "description": "The <b>Component.tag</b> property allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Prefer using <b>CompareTag()</b> instead, as this does not result in managed allocations."}, {"id": "PAC0231", "type": "UnityEngine.Object", "method": "name", "areas": ["Memory"], "description": "The <b>Object.name</b> property allocates managed memory.", "recommendation": "Try to avoid getting this property in frequently-updated code. Ideally, this property should only be used during initialisation, and the results should be cached if they need to be re-used."}, {"id": "PAC1000", "type": "System.Linq", "method": "*", "areas": ["CPU", "Memory"], "description": "Linq allocates large amounts of managed memory and exhibits poor CPU performance.", "recommendation": "We strongly advise against using Linq in any frequently-updated code. Ban its usage from the project entirely, or confine it to initialization code and use it sparingly."}, {"id": "PAC1001", "type": "System.Reflection", "method": "*", "areas": ["CPU"], "description": "Reflection is slow, and not generally considered performant enough for runtime code.", "recommendation": "Remove code which relies on reflection, or minimise its usage, particularly outside of initialization."}, {"id": "PAC1002", "type": "System.String", "method": "Concat", "areas": ["Memory"], "defaultSeverity": 5, "description": "String concatenation operations allocates managed memory.", "recommendation": "Try to avoid concatenating strings in frequently-updated code. Prefer using a StringBuilder instead, as this minimizes managed allocations."}, {"id": "PAC1003", "type": "System.DateTime", "method": "Now", "areas": ["CPU"], "description": "<b>System.DateTime.Now</b> can take a lot of CPU time because it needs to figure out the current timezone and daylight saving time information.", "recommendation": "Try to avoid using this method in frequently-updated code. Prefer UnityEngine.Time.time or, if precise time is needed, use DateTime.UtcNow."}, {"id": "PAC1004", "type": "System.AppDomain", "method": "GetAssemblies", "areas": ["CPU"], "description": "<b>System.AppDomain.GetAssemblies</b> can take a lot of CPU time.", "recommendation": "Try to minimize calls to <b>System.AppDomain.GetAssemblies</b> by caching the returned assemblies. When possible, use <b>UnityEditor.TypeCache</b> for fast access to types, methods and fields."}, {"id": "PAC0232", "type": "UnityEditor.AssetDatabase", "method": "FindAssets", "areas": ["CPU"], "description": "<b>UnityEditor.AssetDatabase.FindAssets</b> is a CPU-intensive operation and can slow down the Editor for long periods of time on large projects.", "recommendation": "Try to minimize calls to <b>UnityEditor.AssetDatabase.FindAssets</b>."}, {"id": "PAC0234", "type": "UnityEngine.Object", "method": "FindObjectOfType", "areas": ["CPU", "Memory"], "description": "<b>Object.FindObjectOfType()</b> allocates managed memory and can be slow.", "recommendation": "Try to avoid calling this method in frequently-updated code. Ideally, this method should only be used during initialisation, and the results should be cached if they need to be re-used."}]