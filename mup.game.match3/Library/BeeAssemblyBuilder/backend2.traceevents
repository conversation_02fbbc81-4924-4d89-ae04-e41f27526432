{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1753501131204150, "dur":1384, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753501131205540, "dur":71, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753501131205617, "dur":87, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753501131205704, "dur":95986, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753501131301772, "dur":208, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1753501131205564, "dur":143, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753501131205874, "dur":132, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1753501131205709, "dur":297, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753501131206426, "dur":107, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2" }}
,{ "pid":12345, "tid":1, "ts":1753501131206674, "dur":133, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1753501131206807, "dur":94622, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753501131301462, "dur":178, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":2, "ts":1753501131205571, "dur":140, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753501131205715, "dur":898, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753501131206613, "dur":768, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753501131207381, "dur":94310, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753501131205578, "dur":139, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753501131205718, "dur":867, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753501131206688, "dur":168, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":3, "ts":1753501131207050, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131207455, "dur":478, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131207954, "dur":53, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131208189, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131208419, "dur":55, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131208495, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131208556, "dur":58, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131208930, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131209104, "dur":481, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131212414, "dur":372, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131212841, "dur":157, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Assets/Packages/R3.1.3.0/lib/netstandard2.1/R3.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131213143, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131213232, "dur":178, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131213411, "dur":206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Domain_Reload.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131213618, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/LitMotion.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131213864, "dur":128, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/R3.Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131213992, "dur":143, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.Addressables.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131214136, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131214284, "dur":119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.DOTween.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131214404, "dur":1071, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.Linq.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131215475, "dur":153, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.TextMeshPro.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131215727, "dur":145, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.PixelPerfect.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131215917, "dur":475, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.Tilemap.Extras.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131216440, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Burst.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131216527, "dur":123, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Collections.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131216651, "dur":1534, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131218186, "dur":213, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131218420, "dur":183, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131218625, "dur":810, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131219460, "dur":214, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131219717, "dur":185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131219903, "dur":142, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131220045, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.2D.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131220142, "dur":198, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131220355, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.ResourceManager.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131220406, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.ScriptableBuildPipeline.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131220582, "dur":117, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131220700, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131220781, "dur":562, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131221369, "dur":292, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll" }}
,{ "pid":12345, "tid":3, "ts":1753501131206856, "dur":14851, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1753501131221772, "dur":78940, "ph":"X", "name": "Csc",  "args": { "detail":"Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1753501131301447, "dur":169, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":4, "ts":1753501131205584, "dur":143, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753501131205728, "dur":872, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753501131206600, "dur":770, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753501131207370, "dur":94335, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753501131205591, "dur":155, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753501131205761, "dur":847, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753501131206608, "dur":769, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753501131207377, "dur":94316, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753501131205598, "dur":181, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753501131205781, "dur":885, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753501131206666, "dur":725, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753501131207391, "dur":94325, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753501131205605, "dur":177, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753501131205785, "dur":968, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753501131206753, "dur":256, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753501131207009, "dur":94675, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753501131205612, "dur":173, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753501131205786, "dur":904, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753501131206690, "dur":697, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753501131207388, "dur":94281, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753501131302218, "dur":87, "ph":"X", "name": "ProfilerWriteOutput" }
,