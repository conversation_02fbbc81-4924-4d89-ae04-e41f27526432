# Programming with Project Auditor

Use the classes and methods in the Scripting API to customize how Project Auditor runs.

|**Topic**|**Description**|
|---|---|
|**[Run Project Auditor from the command line](run-from-command-line.md)**|Start Project Auditor from the command line and customize the data it collects.|
|**[Compare issues and insights](compare-issues.md)**|Use the `Report` class to compare issues that Project Auditor finds.|
|**[Create custom analyzers](custom-analyzers.md)**|Add your own custom analyzers to Project Auditor.|

## Additional resources

* [Project Auditor introduction](project-auditor-introduction.md)
* [Analyze your project](analyze-project.md)