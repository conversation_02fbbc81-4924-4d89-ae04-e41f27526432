using System;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Unity.ProjectAuditor.Editor.Core
{
    class AreasJsonConverter : JsonConverter
    {
        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            var array = JArray.Load(reader);
            var areas = Areas.None;

            foreach (var item in array)
            {
                Areas area;
                if (Enum.TryParse(item.ToString(), out area))
                {
                    areas |= area;
                }
            }

            return areas;
        }

        public override void Write<PERSON><PERSON>(JsonWriter writer, Object value, JsonSerializer serializer)
        {
            var flags = value.ToString()
                .Split(new[] { ", " }, StringSplitOptions.RemoveEmptyEntries)
                .Select(f => $"\"{f}\"");
            writer.WriteRawValue($"[{string.Join(", ", flags)}]");
        }

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(Areas);
        }
    }
}
