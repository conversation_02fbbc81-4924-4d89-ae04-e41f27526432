m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638890974830000000
    m_Hash: 3665463189
  m_LockFileStatus:
    m_FilePath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638890974830000000
    m_Hash: 54159430
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Packages
m_EditorVersion: 6000.0.51f1 (01c3ff5872c5)
m_ResolvedPackages:
- packageId: com.annulusgames.lit-motion@https://github.com/annulusgames/LitMotion.git?path=src/LitMotion/Assets/LitMotion
  testable: 0
  isDirectDependency: 1
  version: 2.0.1
  source: 5
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.annulusgames.lit-motion@7688ea6127fb
  assetPath: Packages/com.annulusgames.lit-motion
  name: com.annulusgames.lit-motion
  displayName: LitMotion
  author:
    name: Annulus Games
    email: 
    url: 
  category: 
  type: 
  description: Lightning-fast and Zero Allocation Tween Library for Unity.
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.6.0
  - name: com.unity.collections
    version: 1.5.1
  - name: com.unity.mathematics
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords:
  - dots
  - tween
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: b651c5866fe0e3fba065ce2e56dfdfcd59d3d109
    revision: HEAD
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 7688ea6127fbfdd222f17f4080da55cc75e74e91
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.cysharp.r3@https://github.com/Cysharp/R3.git?path=src/R3.Unity/Assets/R3.Unity
  testable: 0
  isDirectDependency: 1
  version: 1.3.0
  source: 5
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.cysharp.r3@96346038d239
  assetPath: Packages/com.cysharp.r3
  name: com.cysharp.r3
  displayName: R3
  author:
    name: Cysharp, Inc.
    email: 
    url: https://cysharp.co.jp/en/
  category: Scripting
  type: 
  description: Reactive Extensions for Unity.
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - rx
  - event
  - Scripting
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: fbc12b2c0b6a1ee5ce7d534f9db936bc59069219
    revision: HEAD
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 96346038d2394c17468be16d08af95d812692375
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.cysharp.unitask@https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask
  testable: 0
  isDirectDependency: 1
  version: 2.5.10
  source: 5
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.cysharp.unitask@86b6e6a2e286
  assetPath: Packages/com.cysharp.unitask
  name: com.cysharp.unitask
  displayName: UniTask
  author:
    name: Cysharp, Inc.
    email: 
    url: https://cysharp.co.jp/en/
  category: Task
  type: 
  description: Provides an efficient async/await integration to Unity.
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - async/await
  - async
  - Task
  - UniTask
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: f213ff497e4ff462a77319cf677cf20cc0860ca9
    revision: HEAD
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 86b6e6a2e2863facad59648b5c1bc1b9e91e6514
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2018.4.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.github-glitchenzo.nugetforunity@https://github.com/GlitchEnzo/NuGetForUnity.git?path=/src/NuGetForUnity
  testable: 0
  isDirectDependency: 1
  version: 4.4.0
  source: 5
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.github-glitchenzo.nugetforunity@21b3bb0ec087
  assetPath: Packages/com.github-glitchenzo.nugetforunity
  name: com.github-glitchenzo.nugetforunity
  displayName: NuGetForUnity
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A NuGet Package Manager for Unity
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nuget
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: https://github.com/GlitchEnzo/NuGetForUnity
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: f789083c31250c83082da9b29be0c976152d699b
    revision: HEAD
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 21b3bb0ec0878ac8f34ad763c4b0748825f8dbd8
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2018.4.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.addressables@2.5.0
  testable: 0
  isDirectDependency: 1
  version: 2.5.0
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.addressables@2581a6be7421
  assetPath: Packages/com.unity.addressables
  name: com.unity.addressables
  displayName: Addressables
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'The Addressable Asset System allows the developer to ask for an asset
    via its address. Once an asset (e.g. a prefab) is marked "addressable", it generates
    an address which can be called from anywhere. Wherever the asset resides (local
    or remote), the system will locate it and its dependencies, then return it.


    Use
    ''Window->Asset Management->Addressables'' to begin working with the system.


    Addressables
    use asynchronous loading to support loading from any location with any collection
    of dependencies. Whether you have been using direct references, traditional asset
    bundles, or Resource folders, addressables provide a simpler way to make your
    game more dynamic. Addressables simultaneously opens up the world of asset bundles
    while managing all the complexity.


    For usage samples, see github.com/Unity-Technologies/Addressables-Sample'
  errors: []
  versions:
    all:
    - 0.0.8-preview
    - 0.0.12-preview
    - 0.0.15-preview
    - 0.0.16-preview
    - 0.0.18-preview
    - 0.0.22-preview
    - 0.0.26-preview
    - 0.0.27-preview
    - 0.1.2-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.3.5-preview
    - 0.4.6-preview
    - 0.4.8-preview
    - 0.5.2-preview
    - 0.5.3-preview
    - 0.6.6-preview
    - 0.6.7-preview
    - 0.6.8-preview
    - 0.7.4-preview
    - 0.7.5-preview
    - 0.8.4-preview
    - 0.8.6-preview
    - 1.1.3-preview
    - 1.1.4-preview
    - 1.1.5
    - 1.1.7
    - 1.1.9
    - 1.1.10
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.3.3
    - 1.3.8
    - 1.4.0
    - 1.5.0
    - 1.5.1
    - 1.6.0
    - 1.6.2
    - 1.7.4
    - 1.7.5
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.9.2
    - 1.10.0
    - 1.11.2
    - 1.12.0
    - 1.13.1
    - 1.14.2
    - 1.15.1
    - 1.16.1
    - 1.16.6
    - 1.16.7
    - 1.16.8
    - 1.16.10
    - 1.16.12
    - 1.16.13
    - 1.16.15
    - 1.16.16
    - 1.16.19
    - 1.17.0-preview
    - 1.17.2-preview
    - 1.17.4-preview
    - 1.17.5-preview
    - 1.17.6-preview
    - 1.17.13
    - 1.17.15
    - 1.17.17
    - 1.18.2
    - 1.18.4
    - 1.18.9
    - 1.18.11
    - 1.18.13
    - 1.18.15
    - 1.18.16
    - 1.18.19
    - 1.19.4
    - 1.19.6
    - 1.19.9
    - 1.19.11
    - 1.19.13
    - 1.19.14
    - 1.19.15
    - 1.19.17
    - 1.19.18
    - 1.19.19
    - 1.20.0
    - 1.20.3
    - 1.20.5
    - 1.21.1
    - 1.21.2
    - 1.21.3
    - 1.21.8
    - 1.21.9
    - 1.21.10
    - 1.21.12
    - 1.21.14
    - 1.21.15
    - 1.21.17
    - 1.21.18
    - 1.21.19
    - 1.21.20
    - 1.21.21
    - 1.22.2
    - 1.22.3
    - 1.23.1
    - 1.24.0
    - 1.25.0
    - 1.25.1
    - 2.0.3
    - 2.0.4
    - 2.0.6
    - 2.0.8
    - 2.1.0
    - 2.2.2
    - 2.3.0
    - 2.3.1
    - 2.3.7
    - 2.3.16
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.4.4
    - 2.4.5
    - 2.4.6
    - 2.5.0
    - 2.6.0
    compatible:
    - 2.5.0
    - 2.6.0
    recommended: 2.5.0
    deprecated: []
  dependencies:
  - name: com.unity.profiling.core
    version: 1.0.2
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.scriptablebuildpipeline
    version: 2.4.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.profiling.core
    version: 1.0.2
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.scriptablebuildpipeline
    version: 2.4.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  keywords:
  - asset
  - resources
  - bundle
  - bundles
  - assetbundles
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638830123034740000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.addressables@2.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/Addressables.git
    revision: 123eb0eba0991600ff0d8fa44d2d4aa9aaa7b981
    path: 
  unityLifecycle:
    version: 2.5.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Added IP ping timeout to the Hosting Services window.\n-
    Fixed memory leak when loading Sprite objects from a SpriteAtlas asset.\n- Fixed
    sub-object loading from AssetReferences for types that are not Sprites in a SpriteAtlas.\n-
    Fixed Exception when using third build and load path variables option.\n- Fixed
    bug where identical locations were calculated multiple times in the download
    size\n- Fixed bug where we''d fetch the remote hash at start-up even though catalog
    updates were disabled"}'
  assetStore:
    productId: 
  fingerprint: 2581a6be742198221d66a5688fd5dff18eff1057
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.5.0
    minimumUnityVersion: 2023.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.feature.2d@2.0.1
  testable: 0
  isDirectDependency: 1
  version: 2.0.1
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.feature.2d@dd1ea8910f12
  assetPath: Packages/com.unity.feature.2d
  name: com.unity.feature.2d
  displayName: 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: Import images including multi-layered Photoshop files as Sprites and
    configure them to create 2D games. Create freeform, tile-based and spline-based
    2D game worlds. Create frame-by-frame and bone-based animated characters. Integrated
    with 2D physics to support simulations with colliders and joints. Supports the
    needs of a range of 2D art styles, including pixel art.
  errors: []
  versions:
    all:
    - 2.0.1
    compatible:
    - 2.0.1
    recommended: 2.0.1
    deprecated: []
  dependencies:
  - name: com.unity.2d.animation
    version: default
  - name: com.unity.2d.pixel-perfect
    version: default
  - name: com.unity.2d.psdimporter
    version: default
  - name: com.unity.2d.sprite
    version: default
  - name: com.unity.2d.spriteshape
    version: default
  - name: com.unity.2d.tilemap
    version: default
  - name: com.unity.2d.tilemap.extras
    version: default
  - name: com.unity.2d.aseprite
    version: default
  resolvedDependencies:
  - name: com.unity.2d.animation
    version: 10.1.4
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.2d.pixel-perfect
    version: 5.0.3
  - name: com.unity.2d.psdimporter
    version: 9.0.3
  - name: com.unity.2d.spriteshape
    version: 10.0.7
  - name: com.unity.modules.physics2d
    version: 1.0.0
  - name: com.unity.2d.tilemap
    version: 1.0.0
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.2d.tilemap.extras
    version: 4.1.0
  - name: com.unity.2d.aseprite
    version: 1.1.9
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/2DFeature.html"}'
  assetStore:
    productId: 
  fingerprint: dd1ea8910f12f021c166e8d0d78de44f1390ff6b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.1
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ide.visualstudio@2.0.23
  testable: 0
  isDirectDependency: 1
  version: 2.0.23
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.ide.visualstudio@198cdf337d13
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.23
    recommended: 2.0.23
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638786696173840000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 0fe3b29f9aff2b90b9f0962ae35036a824d3dd6b
    path: 
  unityLifecycle:
    version: 2.0.23
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"Integration:\n\n- Monitor `additionalfile` extension
    by default.\n- Try opening a Visual Studio Code workspace if there''s one (`.code-workspace`
    file in the Unity project).\n\nProject generation:\n\n- Identify `asset`, `meta`,
    `prefab` and `unity` files as `yaml` (Visual Studio Code).\n- Add `sln`/`csproj`
    file nesting (Visual Studio Code).\n- Improve SDK style project generation."}'
  assetStore:
    productId: 
  fingerprint: 198cdf337d13c83ca953581515630d66b779e92b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.23
    minimumUnityVersion: 2019.4.25f1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.inputsystem@1.14.1
  testable: 0
  isDirectDependency: 1
  version: 1.14.1
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.inputsystem@978211393e28
  assetPath: Packages/com.unity.inputsystem
  name: com.unity.inputsystem
  displayName: Input System
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A new input system which can be used as a more extensible and customizable
    alternative to Unity's classic input system in UnityEngine.Input.
  errors: []
  versions:
    all:
    - 0.1.2-preview
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.6-preview
    - 0.2.8-preview
    - 0.2.10-preview
    - 0.9.0-preview
    - 0.9.1-preview
    - 0.9.2-preview
    - 0.9.3-preview
    - 0.9.4-preview
    - 0.9.5-preview
    - 0.9.6-preview
    - 1.0.0-preview
    - 1.0.0-preview.1
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.0-preview.5
    - 1.0.0-preview.6
    - 1.0.0-preview.7
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.1.0-pre.5
    - 1.1.0-pre.6
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.1
    - 1.2.0
    - 1.3.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.5.0
    - 1.5.1
    - 1.6.1
    - 1.6.3
    - 1.7.0
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.9.0
    - 1.10.0
    - 1.11.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.0
    - 1.13.1
    - 1.14.0
    - 1.14.1
    compatible:
    - 1.14.0
    - 1.14.1
    recommended: 1.14.1
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - input
  - events
  - keyboard
  - mouse
  - gamepad
  - touch
  - vr
  - xr
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638877313747820000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.inputsystem@1.14/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/InputSystem.git
    revision: 5f79385f7037a45b3871a30c4bacfbfa0aedb978
    path: 
  unityLifecycle:
    version: 1.14.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n\n- Support for Xbox controllers over USB
    on macOS, using macOS''s default driver. [ISXB-1548]\n\n### Fixed\n- Fixed an
    analytics event being invoked twice when the Save button in the Actions view
    was pressed. [ISXB-1378](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1378)\n-
    Fixed an issue causing a number of errors to be displayed when using `InputTestFixture`
    in playmode tests with domain reloading disabled on playmode entry. [ISXB-1446](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1446)\n-
    Fixed issue where user was not prompted to save changes when loading a second
    input actions asset into an already opened editor. [ISXB-1343](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1343)\n-
    Fixed the on hover behaviour of the two plus buttons in the Input Actions Editor
    window [ISXB-1327](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1327)\n-
    Fixed an issue on macOS which didn''t detect up-left DPAD presses for Xbox controllers.
    [ISXB-810](https://issuetracker.unity3d.com/issues/macos-d-pad-upper-left-corner-is-not-logged-with-the-xbox-controller)\n-
    Fixed Input Actions code generation overwriting user files when the names happened
    to match. [ISXB-1257](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1257)\n-
    Fixed Input Actions code generation using locale-dependent rules when lowercasing
    and uppercasing strings. [ISXB-1406]\n- Fixed an issue when providing JoinPlayer
    with a specific split screen index. [ISXB-897](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-897)\n-
    Fixed Inspector Window being refreshed all the time when a PlayerInput component
    is present with Invoke Unity Events nofication mode chosen [ISXB-1448](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1448)\n-
    Fixed an issue where an action with a name containing a slash \"/\" could not
    be found via `InputActionAsset.FindAction(string,bool)`. [ISXB-1306](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1306).\n-
    Fixed Gamepad stick up/down inputs that were not recognized in WebGL. [ISXB-1090](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1090)\n-
    Fixed reenabling the VirtualMouseInput component may sometimes lead to NullReferenceException.
    [ISXB-1096](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1096)\n-
    Fixed the default button press point not being respected in Editor (as well as
    some other Touchscreen properties). [ISXB-1152](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1152)\n-
    Fixed the TreeView compilation warnings when used with Unity 6.2 beta (ISX-2320)\n-
    Fixed actions being reset when disabling the InputSystemUIInputModule component
    [ISXB-1493](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1493)\n-
    Fixed a memory leak when disabling and enabling the InputSystemUIInputModule
    component at runtime [ISXB-1573](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1573)\n-
    Fixed all InputControls being changed at once when you change just one by reverting
    `2a37caac288ac09bc9122234339dc5df8d3a0ca6`, which was an attempt at fixing [ISXB-1221]
    that introduced this regression [ISXB-1531] (https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1493)\n-
    Fixed PlayerInput component automatically switching away from the default ActionMap
    set to ''None''.\n- Fixed a console error being shown when targeting visionOS
    builds in 2022.3.\n- Fixed a Tap Interaction issue with analog controls. The
    Tap interaction would keep re-starting after timeout. [ISXB-627](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-627)\n-
    Fixed the defaultActionMap dropdown in the PlayerInput component defaulting to
    <None> instead of the first ActionMap.\n- Fixed TrackedPoseDriver stops updating
    position and rotation when device is added after its initialization. [ISXB-1555](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1555)\n-
    Fixed PlayerInput component not working with C# Wrappers (ISXB-1535). This reverted
    changes done to fix [ISXB-920](https://issuetra"}'
  assetStore:
    productId: 
  fingerprint: 978211393e28699aba338ed157cef347ac565bbf
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.14.0
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.project-auditor@1.0.1
  testable: 0
  isDirectDependency: 1
  version: 1.0.1
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.project-auditor@94c6e4e98816
  assetPath: Packages/com.unity.project-auditor
  name: com.unity.project-auditor
  displayName: Project Auditor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'Project Auditor is a static analysis tool that analyzes assets, settings,
    and scripts of the Unity project and produces a report containing: Code and Settings
    Diagnostics, the last BuildReport, and assets information.'
  errors: []
  versions:
    all:
    - 0.10.0
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0
    - 1.0.1
    compatible:
    - 0.10.0
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.0.0-pre.7
    - 1.0.0
    - 1.0.1
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.nuget.mono-cecil
    version: 1.10.1
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  resolvedDependencies:
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords:
  - performance
  - analysis
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638773883996110000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.project-auditor@1.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/ProjectAuditor.git
    revision: 7b3e602b8da35bf1c15ed936c802cd1c91a0e007
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n* Don''t suggest switching Physics 2D
    update mode to Script for now.\n\n### Fixed\n* Fixed an issue where Project Auditor
    exhibited compilation issues on certain versions of the Editor.\n* Fixed link
    to help pages.\n* Fix for PROFB-273; Change summary screen message when an area
    is not analyzed.\n* Fix for PROFB-274; Added dividers between the foldouts on
    the shader variant page.\n* Fix for PROFB-277; change text for \"1 ignored are
    hidden\".\n* Fix for PROFB-278; 1 items changed to 1 item(s) in the table.\n*
    Fix for PROFB-279; Build steps all show as info until you hit refresh.\n* Fix
    for PROFB-280; Removed Path option from the Build Size context menu.\n* Fix for
    PROFB-281; Unable to sort columns on Build - Build Size screens.\n* Fix for PROFB-284;
    change references to Texture Streaming when it''s now called Mipmap Streaming.\n*
    Fix for PROFB-285; fix for not being able to hide info compiler messages in the
    compiler messages view.\n* Fix for PROFB-286; fix performance issues when selecting
    lots of table items.\n* Fix for PROFB-287; improve documentation for installation
    instructions.\n* Fix for PROFB-289; exclude shaders that are Hidden from asset
    issues.\n* Fix for PROFB-290; fix settings links for Physics/Physics 2D.\n* Fix
    for PROFB-291; issue where rendering path recommendations would show for the
    wrong pipeline.\n* Fix for PROFB-300; file extensions would sometimes throw because
    a file didn''t have one.\n* Fix for PROFB-310; fix for Analyzing assets causing
    shader issues to vanish.\n* Fix for PROFB-314; speculative attempt to fix out
    of memory crash reported on Discussions."}'
  assetStore:
    productId: 
  fingerprint: 94c6e4e9881619d6fd9744d93bd32bec6a2d676c
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 0.10.0
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.render-pipelines.universal@17.0.4
  testable: 0
  isDirectDependency: 1
  version: 17.0.4
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.render-pipelines.universal@a55da47cc43f
  assetPath: Packages/com.unity.render-pipelines.universal
  name: com.unity.render-pipelines.universal
  displayName: Universal Render Pipeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Universal Render Pipeline (URP) is a prebuilt Scriptable Render
    Pipeline, made by Unity. URP provides artist-friendly workflows that let you
    quickly and easily create optimized graphics across a range of platforms, from
    mobile to high-end consoles and PCs.
  errors: []
  versions:
    all:
    - 17.0.4
    compatible:
    - 17.0.4
    recommended: 17.0.4
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.shadergraph
    version: 17.0.4
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.shadergraph
    version: 17.0.4
  - name: com.unity.searcher
    version: 4.9.3
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  keywords:
  - graphics
  - performance
  - rendering
  - mobile
  - render
  - pipeline
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: a55da47cc43f82fb2bcd4d2e0c610e473c0f6880
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.4
    minimumUnityVersion: 6000.0.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ugui@2.0.0
  testable: 0
  isDirectDependency: 1
  version: 2.0.0
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.ugui@a0f5d16b3c82
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  errors: []
  versions:
    all:
    - 2.0.0
    compatible:
    - 2.0.0
    recommended: 2.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  - TextMeshPro
  - TextMesh Pro
  - Text
  - TMP
  - SDF
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: a0f5d16b3c8274d62d6bf6afdaa2eeef242bbd2b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.0
    minimumUnityVersion: 2019.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.accessibility@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.accessibility
  assetPath: Packages/com.unity.modules.accessibility
  name: com.unity.modules.accessibility
  displayName: Accessibility
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Accessibility module includes utilities to facilitate the development
    of accessible user experiences in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AccessibilityModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.androidjni
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.animation
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.assetbundle
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.audio
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imageconversion
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods for converting image data. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imgui
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.jsonserialize
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.particlesystem
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics2d
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.screencapture
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.ui
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequest
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestassetbundle
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestaudio
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequesttexture
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestwww
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.render-pipelines.core@17.0.4
  testable: 0
  isDirectDependency: 0
  version: 17.0.4
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.render-pipelines.core@2be5e7224a10
  assetPath: Packages/com.unity.render-pipelines.core
  name: com.unity.render-pipelines.core
  displayName: Scriptable Render Pipeline Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: SRP Core makes it easier to create or customize a Scriptable Render
    Pipeline (SRP). SRP Core contains reusable code, including boilerplate code for
    working with platform-specific graphics APIs, utility functions for common rendering
    operations, and  shader libraries. The code in SRP Core is use by the High Definition
    Render Pipeline (HDRP) and Universal Render Pipeline (URP). If you are creating
    a custom SRP from scratch or customizing a prebuilt SRP, using SRP Core will
    save you time.
  errors: []
  versions:
    all:
    - 17.0.4
    compatible:
    - 17.0.4
    recommended: 17.0.4
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.20
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.collections
    version: 2.4.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 2be5e7224a10830881453cbaf6898e584c3addb6
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.4
    minimumUnityVersion: 6000.0.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.shadergraph@17.0.4
  testable: 0
  isDirectDependency: 0
  version: 17.0.4
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.shadergraph@052d95cfe62a
  assetPath: Packages/com.unity.shadergraph
  name: com.unity.shadergraph
  displayName: Shader Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Shader Graph package adds a visual Shader editing tool to Unity.
    You can use this tool to create Shaders in a visual way instead of writing code.
    Specific render pipelines can implement specific graph features. Currently, both
    the High Definition Rendering Pipeline and the Universal Rendering Pipeline support
    Shader Graph.
  errors: []
  versions:
    all:
    - 17.0.4
    compatible:
    - 17.0.4
    recommended: 17.0.4
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.searcher
    version: 4.9.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 052d95cfe62aa3664d6a823074c5806f7597e6a4
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.4
    minimumUnityVersion: 6000.0.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.render-pipelines.universal-config@17.0.3
  testable: 0
  isDirectDependency: 0
  version: 17.0.3
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.render-pipelines.universal-config@dd206bf35d04
  assetPath: Packages/com.unity.render-pipelines.universal-config
  name: com.unity.render-pipelines.universal-config
  displayName: Universal Render Pipeline Config
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Configuration files for the Universal Render Pipeline.
  errors: []
  versions:
    all:
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: dd206bf35d0451204981fdb31d4c6831a0ee2a7e
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.nuget.mono-cecil@1.11.4
  testable: 0
  isDirectDependency: 0
  version: 1.11.4
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f
  assetPath: Packages/com.unity.nuget.mono-cecil
  name: com.unity.nuget.mono-cecil
  displayName: Mono Cecil
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'The mono cecil library from https://www.nuget.org/packages/Mono.Cecil/


    This
    package is intended for internal Unity use only. Most Unity users will be better
    suite using the existing community tooling.

    To avoid assembly clashes, please
    use this package if you intend to use Mono.Cecil.'
  errors: []
  versions:
    all:
    - 0.1.6-preview.2
    - 1.0.0-preview.1
    - 1.10.0-preview.1
    - 1.10.1-preview.1
    - 1.10.1
    - 1.10.2
    - 1.11.4
    - 1.11.5
    compatible:
    - 1.11.4
    - 1.11.5
    recommended: 1.11.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637852971930000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.mono-cecil.git
    revision: d0133ce672d724694b56bfd20672acf6f8737fec
    path: 
  unityLifecycle:
    version: 1.11.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.11.4
    minimumUnityVersion: 2018.4.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.nuget.newtonsoft-json@3.2.1
  testable: 0
  isDirectDependency: 0
  version: 3.2.1
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0
  assetPath: Packages/com.unity.nuget.newtonsoft-json
  name: com.unity.nuget.newtonsoft-json
  displayName: Newtonsoft Json
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'Newtonsoft Json for use in Unity projects and Unity packages. Currently
    synced to version 13.0.2.


    This package is used for advanced json serialization
    and deserialization. Most Unity users will be better suited using the existing
    json tools built into Unity.

    To avoid assembly clashes, please use this
    package if you intend to use Newtonsoft Json.'
  errors: []
  versions:
    all:
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.1-preview.1
    - 1.1.2
    - 2.0.0-preview
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0
    - 2.0.1-preview.1
    - 2.0.2
    - 3.0.1
    - 3.0.2
    - 3.1.0
    - 3.2.0
    - 3.2.1
    compatible:
    - 3.2.1
    recommended: 3.2.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 638186318800000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.newtonsoft-json.git
    revision: d8e49aef8979bef617144382052ec2f479645eaf
    path: 
  unityLifecycle:
    version: 3.2.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"* Fixed Newtonsoft DLL when compiling with netstandard
    2.0."}'
  assetStore:
    productId: 
  fingerprint: 74deb55db2a0c29ddfda576608bcb86abbd13ee6
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.2.1
    minimumUnityVersion: 2018.4.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.uielements
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.test-framework@1.5.1
  testable: 0
  isDirectDependency: 0
  version: 1.5.1
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.test-framework@dfdbd02f5918
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: 
  description: Test framework for running Edit mode and Play mode tests in Unity.
  errors: []
  versions:
    all:
    - 1.5.1
    compatible:
    - 1.5.1
    recommended: 1.5.1
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 2.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: dfdbd02f59180baa5668370b8f71f6fbeaa3b032
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.5.1
    minimumUnityVersion: 2022.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.animation@10.1.4
  testable: 0
  isDirectDependency: 0
  version: 10.1.4
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.animation@494a3b4e73a9
  assetPath: Packages/com.unity.2d.animation
  name: com.unity.2d.animation
  displayName: 2D Animation
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Animation provides all the necessary tooling and runtime components
    for skeletal animation using Sprites.
  errors: []
  versions:
    all:
    - 1.0.15-preview.1
    - 1.0.15-preview.2
    - 1.0.15-preview.3
    - 1.0.15-preview.4
    - 1.0.15-preview.5
    - 1.0.16-preview
    - 1.0.16-preview.1
    - 1.0.16-preview.2
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0-preview.3
    - 2.1.0-preview.1
    - 2.1.0-preview.2
    - 2.1.0-preview.4
    - 2.1.0-preview.5
    - 2.1.0-preview.7
    - 2.2.0-preview.1
    - 2.2.0-preview.4
    - 2.2.0-preview.5
    - 2.2.1-preview.1
    - 2.2.1-preview.2
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.8
    - 3.1.0
    - 3.1.1
    - 3.2.1
    - 3.2.2
    - 3.2.3
    - 3.2.4
    - 3.2.5
    - 3.2.6
    - 3.2.9
    - 3.2.10
    - 3.2.11
    - 3.2.13
    - 3.2.14
    - 3.2.15
    - 3.2.16
    - 3.2.17
    - 3.2.18
    - 4.0.0
    - 4.0.1
    - 4.1.0
    - 4.1.1
    - 4.2.1
    - 4.2.2
    - 4.2.3
    - 4.2.4
    - 4.2.5
    - 4.2.6
    - 4.2.8
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.0.3
    - 5.0.4
    - 5.0.5
    - 5.0.6
    - 5.0.7
    - 5.0.8
    - 5.0.9
    - 5.0.10
    - 5.1.0
    - 5.1.1
    - 5.2.0
    - 5.2.1
    - 5.2.3
    - 5.2.4
    - 5.2.6
    - 5.2.7
    - 6.0.0-pre.1
    - 6.0.0-pre.2
    - 6.0.1
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.7
    - 7.0.0-pre.2
    - 7.0.0-pre.3
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 7.0.4
    - 7.0.5
    - 7.0.6
    - 7.0.7
    - 7.0.8
    - 7.0.9
    - 7.0.10
    - 7.0.11
    - 7.0.12
    - 7.0.13
    - 7.1.0
    - 7.1.1
    - 7.1.2
    - 7.2.0
    - 8.0.0-pre.3
    - 8.0.0-pre.4
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.0.5
    - 9.0.0-pre.1
    - 9.0.0-pre.3
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.1.0
    - 9.1.1
    - 9.1.2
    - 9.1.3
    - 9.2.0
    - 9.2.1
    - 10.0.0-pre.1
    - 10.0.0-pre.2
    - 10.0.0
    - 10.0.1
    - 10.0.2
    - 10.0.3
    - 10.1.0
    - 10.1.1
    - 10.1.2
    - 10.1.3
    - 10.1.4
    - 10.2.0
    - 10.2.1
    - 11.0.0
    - 12.0.0
    - 12.0.1
    - 12.0.2
    compatible:
    - 10.1.4
    - 10.2.0
    - 10.2.1
    recommended: 10.1.4
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.collections
    version: 1.2.4
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords:
  - 2d
  - animation
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459075740000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.animation@10.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: c393ad93bdba3e78ebd30a5ccd2e6da5d8b92aba
    path: 
  unityLifecycle:
    version: 10.1.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Animation Preview window sometimes does
    not display deformed Sprites. (DANB-705)\n- Sprite Resolver missing sprite previews
    when dealing with large number of entries. (DANB-714)\n- Misaligned label previews
    in Sprite Resolver''s inspector. (DANB-722)\n- Sprite Resolver component not
    updated after Sprite Library Asset has been modified. (DANB-727)\n- Sprite Skin
    breaks in the animation preview window after sprite swap. (DANB-743)\n- IK gizmos
    are displayed in the SceneView when IKManager2D is active in Animation Preview
    window. (DANB-738)\n- IK solvers are misaligned when bones have different depths.
    (DANB-753)\n- Rendering issues with SRP Batching and Sprite mask. (DANB-760)\n-
    Unable to drag sprites into empty rows of the Sprite Library Editor. (DANB-749)\n-
    Sprite Skin deformation systems have outdated data after sprite swap. (DANB-766)\n-
    Setting incorrect computer buffer size. (DANB-768)\n- Reenable editor tests.\n-
    Bone buffer binding issues.\n- Sprite changed callback listeners."}'
  assetStore:
    productId: 
  fingerprint: 494a3b4e73a9ae26677ef6e9fd6bff4ca643770a
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 10.1.4
    minimumUnityVersion: 2023.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.pixel-perfect@5.0.3
  testable: 0
  isDirectDependency: 0
  version: 5.0.3
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.pixel-perfect@e3ae982b672d
  assetPath: Packages/com.unity.2d.pixel-perfect
  name: com.unity.2d.pixel-perfect
  displayName: 2D Pixel Perfect
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'The 2D Pixel Perfect package contains the Pixel Perfect Camera component
    which ensures your pixel art remains crisp and clear at different resolutions,
    and stable in motion.


    It is a single component that makes all the calculations
    needed to scale the viewport with resolution changes, removing the hassle from
    the user. The user can adjust the definition of the pixel art rendered within
    the camera viewport through the component settings, as well preview any changes
    immediately in Game view by using the Run in Edit Mode feature.'
  errors: []
  versions:
    all:
    - 1.0.0-preview
    - 1.0.1-preview
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.1.0
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 4.0.0
    - 4.0.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.0.3
    - 5.1.0
    compatible:
    - 5.0.3
    - 5.1.0
    recommended: 5.0.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - pixel
  - perfect
  - 2D
  - sprite
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638023910590000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: 6dea8ac5bd6953acd64f1ac9d470912954b0c015
    path: 
  unityLifecycle:
    version: 5.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: e3ae982b672dc7cca42a6303bdf53b84c69991da
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 5.0.3
    minimumUnityVersion: 2021.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.psdimporter@9.0.3
  testable: 0
  isDirectDependency: 0
  version: 9.0.3
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.psdimporter@676bae148e11
  assetPath: Packages/com.unity.2d.psdimporter
  name: com.unity.2d.psdimporter
  displayName: 2D PSD Importer
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: assets
  description: A ScriptedImporter for importing Adobe Photoshop PSB (Photoshop Big)
    file format. The ScriptedImporter is currently targeted for users who wants to
    create multi Sprite character animation using Unity 2D Animation Package.
  errors: []
  versions:
    all:
    - 1.0.0-preview.1
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.1.0-preview.1
    - 1.1.0-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.3
    - 1.2.0-preview.4
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.1.6
    - 2.1.8
    - 2.1.9
    - 2.1.10
    - 2.1.11
    - 3.0.0
    - 3.1.1
    - 3.1.3
    - 3.1.4
    - 3.1.5
    - 3.1.6
    - 3.1.7
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.1
    - 5.0.3
    - 5.0.4
    - 5.0.6
    - 6.0.0-pre.2
    - 6.0.0-pre.3
    - 6.0.0-pre.4
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.0.9
    - 6.1.0
    - 7.0.0-pre.3
    - 7.0.0-pre.4
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 8.0.0-pre.1
    - 8.0.0-pre.3
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.0.5
    - 8.1.0
    - 8.1.1
    - 9.0.0-pre.1
    - 9.0.0-pre.2
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.1.0
    - 10.0.0
    - 10.1.0
    - 10.1.1
    - 11.0.0
    - 11.0.1
    compatible:
    - 9.0.3
    - 9.1.0
    recommended: 9.0.3
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 9.0.4
  - name: com.unity.2d.sprite
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  - psdimporter
  - assetimporter
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638489453630000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.psdimporter@9.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: cacb780673a1e5d1e9bd0e398015b1a9924c0922
    path: 
  unityLifecycle:
    version: 9.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fix source file cannot be deleted after
    subsequent import. (Case DANB-579)\n\n### Changed\n- Updated the Editor Analytics
    to use the latest APIs."}'
  assetStore:
    productId: 
  fingerprint: 676bae148e11de9a02db5a3614b8c56e4f0f44ac
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 9.0.3
    minimumUnityVersion: 2023.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.sprite@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.sprite@a1146c20a947
  assetPath: Packages/com.unity.2d.sprite
  name: com.unity.2d.sprite
  displayName: 2D Sprite
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: Use Unity Sprite Editor Window to create and edit Sprite asset properties
    like pivot, borders and Physics shape
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - 2d
  - sprite
  - sprite editor window
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: a1146c20a94767d627b9db262ae136cedef9c2c7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2019.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.spriteshape@10.0.7
  testable: 0
  isDirectDependency: 0
  version: 10.0.7
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.spriteshape@9e35352ae135
  assetPath: Packages/com.unity.2d.spriteshape
  name: com.unity.2d.spriteshape
  displayName: 2D SpriteShape
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: SpriteShape Runtime & Editor Package contains the tooling and the
    runtime component that allows you to create very organic looking spline based
    2D worlds. It comes with intuitive configurator and a highly performant renderer.
  errors: []
  versions:
    all:
    - 1.0.10-preview.2
    - 1.0.11-preview
    - 1.0.12-preview
    - 1.0.12-preview.1
    - 1.0.14-preview.2
    - 2.0.0-preview.4
    - 2.0.0-preview.5
    - 2.0.0-preview.7
    - 2.0.0-preview.8
    - 2.0.0-preview.9
    - 2.1.0-preview.2
    - 2.1.0-preview.6
    - 2.1.0-preview.7
    - 2.1.0-preview.10
    - 2.1.0-preview.11
    - 3.0.1
    - 3.0.2
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.8
    - 3.0.9
    - 3.0.10
    - 3.0.11
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.1.4
    - 4.1.5
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.1.0
    - 5.1.1
    - 5.1.2
    - 5.1.3
    - 5.1.4
    - 5.1.5
    - 5.1.6
    - 5.1.7
    - 5.2.0
    - 5.3.0
    - 6.0.0-pre.1
    - 6.0.0-pre.2
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 7.0.0-pre.2
    - 7.0.0-pre.3
    - 7.0.0
    - 7.0.2
    - 7.0.3
    - 7.0.4
    - 7.0.5
    - 7.0.6
    - 7.0.7
    - 7.1.0
    - 8.0.0-pre.4
    - 8.0.0-pre.5
    - 8.0.0
    - 8.0.1
    - 9.0.0-pre.1
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.1.0
    - 9.1.1
    - 10.0.0-pre.1
    - 10.0.0-pre.2
    - 10.0.0
    - 10.0.1
    - 10.0.2
    - 10.0.3
    - 10.0.4
    - 10.0.5
    - 10.0.6
    - 10.0.7
    - 10.1.0
    - 11.0.0
    - 12.0.0
    - 12.0.1
    compatible:
    - 10.0.7
    - 10.1.0
    recommended: 10.0.7
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords:
  - 2d
  - shape
  - spriteshape
  - smartsprite
  - spline
  - terrain2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459104040000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.spriteshape@10.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: baf581b10d05f7aa59c58b39f4b798829f703946
    path: 
  unityLifecycle:
    version: 10.0.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- DANB-731 \"System.IndexOutOfRangeException\"
    is thrown when increasing Sprite Shape Mesh size beyond limits"}'
  assetStore:
    productId: 
  fingerprint: 9e35352ae135f602746220e7edc09eb95bbec530
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 10.0.7
    minimumUnityVersion: 2023.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.tilemap@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.tilemap@91e7def251e0
  assetPath: Packages/com.unity.2d.tilemap
  name: com.unity.2d.tilemap
  displayName: 2D Tilemap Editor
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Tilemap Editor is a package that contains editor functionalities
    for editing Tilemaps.
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  - Tilemap
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 91e7def251e0de922ba309ad7aa18299b1d60ca7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2019.2.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.tilemap.extras@4.1.0
  testable: 0
  isDirectDependency: 0
  version: 4.1.0
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.tilemap.extras@13634da7dbe0
  assetPath: Packages/com.unity.2d.tilemap.extras
  name: com.unity.2d.tilemap.extras
  displayName: 2D Tilemap Extras
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: '2D Tilemap Extras is a package that contains extra scripts for use
    with 2D Tilemap features in Unity. These include custom Tiles and Brushes for
    the Tilemap feature.


    The following are included in the package:

    Brushes:
    GameObject Brush, Group Brush, Line Brush, Random Brush

    Tiles: Animated
    Tile, Rule Tile, Rule Override Tile

    Other: Grid Information, Custom Rules
    for Rule Tile'
  errors: []
  versions:
    all:
    - 1.5.0-preview
    - 1.6.0-preview.1
    - 1.6.1-preview
    - 1.6.2-preview
    - 1.6.3-preview
    - 1.6.4-preview
    - 1.7.0-preview
    - 1.8.0-preview
    - 1.8.1-preview
    - 1.8.2-preview
    - 1.8.3-preview
    - 1.8.4-preview
    - 2.0.0-pre.1
    - 2.0.0-pre.2
    - 2.0.0
    - 2.2.0
    - 2.2.1
    - 2.2.2
    - 2.2.3
    - 2.2.4
    - 2.2.5
    - 2.2.6
    - 2.2.7
    - 2.2.8
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 4.0.0-pre.1
    - 4.0.0-pre.2
    - 4.0.0-pre.3
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.1.0
    - 4.2.1
    - 4.3.0
    - 4.3.1
    - 5.0.0
    - 5.0.1
    compatible:
    - 4.1.0
    recommended: 4.1.0
    deprecated: []
  dependencies:
  - name: com.unity.2d.tilemap
    version: 1.0.0
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.tilemap
    version: 1.0.0
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459126220000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.tilemap.extras@4.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/2d-extras.git
    revision: 8d9a9d9b09b21c40c727e0dfb6188875f796d9f5
    path: 
  unityLifecycle:
    version: 4.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n- [GameObjectBrush] Allow expansion of
    SceneRoot Grid foldout when clicking on label\n- [GridInformation] Fix exception
    when serializing GridInformation component if component is part of a Prefab\n-
    Remove dependency on com.unity.ugui"}'
  assetStore:
    productId: 
  fingerprint: 13634da7dbe06c39bac6bbe2d1a166cf91f58ad7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 4.1.0
    minimumUnityVersion: 6000.0.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.aseprite@1.1.9
  testable: 0
  isDirectDependency: 0
  version: 1.1.9
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.aseprite@996e69f78764
  assetPath: Packages/com.unity.2d.aseprite
  name: com.unity.2d.aseprite
  displayName: 2D Aseprite Importer
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Aseprite Importer is a package which enables the import of .aseprite
    files from the Pixel Art tool Aseprite.
  errors: []
  versions:
    all:
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0
    - 1.0.1
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.4
    - 1.1.5
    - 1.1.6
    - 1.1.7
    - 1.1.8
    - 1.1.9
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 2.0.0
    - 2.0.1
    compatible:
    - 1.1.9
    recommended: 1.1.9
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 6.0.6
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.2.6
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  - Aseprite
  - Pixel
  - Art
  - assetimporter
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638796223739820000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.aseprite@1.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/AsepriteImporter.git
    revision: b3f5769982eceb8fffc54aaa15576646bb8dc938
    path: 
  unityLifecycle:
    version: 1.1.9
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Added support for Aseprite''s new Layer
    UUID, to help identify each Aseprite Layer by id instead of name and path.\n-
    Fixed an issue where going from Import Mode: Individual Layers to Import Mode:
    Merge Frame would cause the importer to fail the import. (DANB-875)\n- Fixed
    an issue where the content in the Aseprite''s Preference menu were misaligned.
    (DANB-880)\n- Fixed an issue where some elements in the Aseprite Importer inspector
    could not be edited when in Debug mode. (DANB-863)"}'
  assetStore:
    productId: 
  fingerprint: 996e69f78764999291cc161902e252662ddc9485
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.1.9
    minimumUnityVersion: 2021.3.15f1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.profiling.core@1.0.2
  testable: 0
  isDirectDependency: 0
  version: 1.0.2
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.profiling.core@aac7b93912bc
  assetPath: Packages/com.unity.profiling.core
  name: com.unity.profiling.core
  displayName: Unity Profiling Core API
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: The Unity Profiling Core package provides an API for code instrumentation
    markup, and for profiling statistic collection.
  errors: []
  versions:
    all:
    - 0.1.0-preview.1
    - 0.2.0-preview.1
    - 0.2.1-preview.1
    - 1.0.0-pre.1
    - 1.0.0
    - 1.0.2
    compatible:
    - 1.0.2
    recommended: 1.0.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - profiler
  - profiling
  - api
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637828752310000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/profiler.git
    revision: 2189ba14439d76a4083f59fae87163b4bdfd49c2
    path: 
  unityLifecycle:
    version: 1.0.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: aac7b93912bc5df5fe06b04ff1b758493cdc2346
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.2
    minimumUnityVersion: 2020.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.scriptablebuildpipeline@2.4.0
  testable: 0
  isDirectDependency: 0
  version: 2.4.0
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.scriptablebuildpipeline@7e8a1cf5a47d
  assetPath: Packages/com.unity.scriptablebuildpipeline
  name: com.unity.scriptablebuildpipeline
  displayName: Scriptable Build Pipeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Scriptable Build Pipeline moves the asset bundle build pipeline
    to C#.  Use the pre-defined build flows, or create your own using the divided
    up APIs.  This system improves build time, fixes incremental build, and provides
    greater flexibility.
  errors: []
  versions:
    all:
    - 0.0.5-preview
    - 0.0.6-preview
    - 0.0.8-preview
    - 0.0.9-preview
    - 0.0.10-preview
    - 0.0.14-preview
    - 0.0.15-preview
    - 0.1.0-preview
    - 0.2.0-preview
    - 1.0.1-preview
    - 1.1.0-preview
    - 1.1.1-preview
    - 1.2.1-preview
    - 1.3.5-preview
    - 1.4.1-preview
    - 1.5.0-preview
    - 1.5.1
    - 1.5.2
    - 1.5.4
    - 1.5.6
    - 1.5.10
    - 1.6.3-preview
    - 1.6.4-preview
    - 1.6.5-preview
    - 1.7.2
    - 1.7.3
    - 1.8.2
    - 1.8.4
    - 1.8.6
    - 1.9.0
    - 1.10.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.1
    - 1.14.0
    - 1.14.1
    - 1.15.1
    - 1.15.2
    - 1.16.1
    - 1.17.0
    - 1.18.0
    - 1.19.0
    - 1.19.1
    - 1.19.2
    - 1.19.3
    - 1.19.4
    - 1.19.5
    - 1.19.6
    - 1.20.1
    - 1.20.2
    - 1.21.0
    - 1.21.1
    - 1.21.2
    - 1.21.3
    - 1.21.5
    - 1.21.7
    - 1.21.8
    - 1.21.9
    - 1.21.20
    - 1.21.21
    - 1.21.22
    - 1.21.23
    - 1.21.24
    - 1.21.25
    - 1.22.1
    - 1.22.2
    - 1.22.4
    - 1.22.5
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.2
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.2.4
    - 2.2.11
    - 2.3.0
    - 2.3.1
    - 2.3.2
    - 2.3.3
    - 2.3.4
    - 2.3.5
    - 2.3.6
    - 2.3.7
    - 2.3.8
    - 2.4.0
    compatible:
    - 2.4.0
    recommended: 2.4.0
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  keywords:
  - build
  - bundle
  - bundles
  - assetbundles
  - cache
  - server
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638830123018510000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.scriptablebuildpipeline@2.4/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/Addressables.git
    revision: 123eb0eba0991600ff0d8fa44d2d4aa9aaa7b981
    path: 
  unityLifecycle:
    version: 2.4.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Avoid recursive dependencies in ContentFiles"}'
  assetStore:
    productId: 
  fingerprint: 7e8a1cf5a47d3fd88c96e0cb21b3d805b63b6834
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.4.0
    minimumUnityVersion: 2023.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.burst@1.8.21
  testable: 0
  isDirectDependency: 0
  version: 1.8.21
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242
  assetPath: Packages/com.unity.burst
  name: com.unity.burst
  displayName: Burst
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Burst is a compiler that translates from IL/.NET bytecode to highly
    optimized native code using LLVM.
  errors: []
  versions:
    all:
    - 0.2.4-preview.5
    - 0.2.4-preview.7
    - 0.2.4-preview.11
    - 0.2.4-preview.12
    - 0.2.4-preview.13
    - 0.2.4-preview.14
    - 0.2.4-preview.15
    - 0.2.4-preview.16
    - 0.2.4-preview.17
    - 0.2.4-preview.18
    - 0.2.4-preview.19
    - 0.2.4-preview.20
    - 0.2.4-preview.21
    - 0.2.4-preview.22
    - 0.2.4-preview.23
    - 0.2.4-preview.24
    - 0.2.4-preview.25
    - 0.2.4-preview.30
    - 0.2.4-preview.31
    - 0.2.4-preview.33
    - 0.2.4-preview.34
    - 0.2.4-preview.37
    - 0.2.4-preview.41
    - 0.2.4-preview.45
    - 0.2.4-preview.48
    - 0.2.4-preview.50
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.1
    - 1.1.2
    - 1.1.3-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.5
    - 1.2.0-preview.6
    - 1.2.0-preview.8
    - 1.2.0-preview.9
    - 1.2.0-preview.10
    - 1.2.0-preview.11
    - 1.2.0-preview.12
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.0-preview.1
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.4
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0-preview.8
    - 1.3.0-preview.9
    - 1.3.0-preview.10
    - 1.3.0-preview.11
    - 1.3.0-preview.12
    - 1.3.0-preview.13
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0-pre.1
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.4
    - 1.4.0-preview.5
    - 1.4.1-pre.1
    - 1.4.1-pre.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4-preview.1
    - 1.4.4-preview.2
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.4.9
    - 1.4.11
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6-preview.1
    - 1.5.6
    - 1.6.0-pre.2
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    compatible:
    - 1.8.21
    - 1.8.22
    - 1.8.23
    recommended: 1.8.23
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638797336625580000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.burst@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/burst.git
    revision: 941bff0925baa46012a2eff522313c170bf36c7a
    path: 
  unityLifecycle:
    version: 1.8.21
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- update EmbeddedLinux argument syntax to support newer
    clang versions\n\n### Added\n- Added LLVM 19 Support\n- Added stack protector
    AOT settings options.\n\n### Removed\n\n### Fixed\n- Fixed hashing error that
    could occur when an assembly contained a type reference with a \"module\" resolution
    scope\n- Fixed internal compiler error when using a `FixedStringNBytes` value
    in an interpolated string\n- Fixed a crash caused by assigning `null` to a `Span`\n-
    Fixed \"Unable to resolve the method\" error that occurred when two or more methods
    had the same name, one of the methods contained a generic parameter, and the
    generic parameter type had two or more generic arguments\n- Fixed handling of
    `stackalloc`ed arrays with initial values for newer versions of Visual Studio\n-
    When building for Android with the Mono scripting backend, arm64-v8a symbols
    could be incorrectly included in the output, even though Mono for Android only
    supports armv7. This is now fixed.\n- Fixed compiler crash that could happen
    when using an interface method that had a default implementation defined in another
    interface\n- Fixed cropping of tooltips in the inspector.\n- Fixed rare \"Unhandled
    exception. System.InvalidCastException: Unable to cast object of type ''System.IO.MemoryStream''
    to type ''System.Text.StringBuilder''\" error that could occur during Burst compilation\n-
    Fixed a `BC1054` error that could occur if a struct with a pointer-to-generic-parameter-typed
    field was used as a generic argument to an interface type\n- Fixed compiler crash
    when trying to use the `Span<T>(T[])` or `Span<T>(T[],int,int)` constructors\n\n###
    Changed\n- EmbeddedLinux SSE4 default\n\n### Known Issues\n- With LLVM 19, Burst''s
    alias analysis is more conservative than it needs to be which may result in performance
    reductions in Burst compiled code. This is will be addressed in the next Burst
    release. Note that at this time, Burst by default uses LLVM 18."}'
  assetStore:
    productId: 
  fingerprint: 59eb6f11d2422f95682320d9daa3e79fdb076744
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.21
    minimumUnityVersion: 2020.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.collections@2.5.1
  testable: 0
  isDirectDependency: 0
  version: 2.5.1
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.collections@56bff8827a7e
  assetPath: Packages/com.unity.collections
  name: com.unity.collections
  displayName: Collections
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A C# collections library providing data structures that can be used
    in jobs, and optimized by Burst compiler.
  errors: []
  versions:
    all:
    - 0.0.9-preview.1
    - 0.0.9-preview.2
    - 0.0.9-preview.3
    - 0.0.9-preview.4
    - 0.0.9-preview.5
    - 0.0.9-preview.6
    - 0.0.9-preview.7
    - 0.0.9-preview.8
    - 0.0.9-preview.9
    - 0.0.9-preview.10
    - 0.0.9-preview.11
    - 0.0.9-preview.12
    - 0.0.9-preview.13
    - 0.0.9-preview.14
    - 0.0.9-preview.15
    - 0.0.9-preview.16
    - 0.0.9-preview.17
    - 0.0.9-preview.18
    - 0.0.9-preview.19
    - 0.0.9-preview.20
    - 0.1.0-preview
    - 0.1.1-preview
    - 0.2.0-preview.13
    - 0.3.0-preview.0
    - 0.4.0-preview.6
    - 0.5.0-preview.9
    - 0.5.1-preview.11
    - 0.5.2-preview.8
    - 0.6.0-preview.9
    - 0.7.0-preview.2
    - 0.7.1-preview.3
    - 0.8.0-preview.5
    - 0.9.0-preview.5
    - 0.9.0-preview.6
    - 0.11.0-preview.17
    - 0.12.0-preview.13
    - 0.14.0-preview.16
    - 0.15.0-preview.21
    - 0.17.0-preview.18
    - 1.0.0-pre.3
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.1.0
    - 1.2.3-pre.1
    - 1.2.3
    - 1.2.4
    - 1.3.1
    - 1.4.0
    - 1.5.1
    - 1.5.2
    - 2.1.0-exp.4
    - 2.1.0-pre.2
    - 2.1.0-pre.6
    - 2.1.0-pre.11
    - 2.1.0-pre.18
    - 2.1.1
    - 2.1.4
    - 2.2.0
    - 2.2.1
    - 2.3.0-exp.1
    - 2.3.0-pre.3
    - 2.4.0-exp.2
    - 2.4.0-pre.2
    - 2.4.0-pre.5
    - 2.4.0
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.5.0-exp.1
    - 2.5.0-pre.2
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    - 2.6.0-pre.4
    compatible:
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    - 2.6.0-pre.4
    recommended: 2.5.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.17
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords:
  - dots
  - collections
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638621282722800000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collections@2.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/dots.git
    revision: 5b0dea6b455f5df005c19fa984ddfa237d6cd707
    path: 
  unityLifecycle:
    version: 2.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n* Updated Burst dependency to version
    1.8.17\n* Updated Unity Test Framework dependency to version 1.4.5\n* Updated
    entities packages dependencies\n\n### Fixed\n* Certain cases would cause an ILPostProcessor
    to fail, blocking compilation, but no more."}'
  assetStore:
    productId: 
  fingerprint: 56bff8827a7ef6d44fcee4f36e558a74da89c1a0
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.5.1
    minimumUnityVersion: 2022.3.11f1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.mathematics@1.3.2
  testable: 0
  isDirectDependency: 0
  version: 1.3.2
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.mathematics@8017b507cc74
  assetPath: Packages/com.unity.mathematics
  name: com.unity.mathematics
  displayName: Mathematics
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Unity's C# SIMD math library providing vector types and math functions
    with a shader like syntax.
  errors: []
  versions:
    all:
    - 0.0.12-preview.2
    - 0.0.12-preview.5
    - 0.0.12-preview.8
    - 0.0.12-preview.10
    - 0.0.12-preview.11
    - 0.0.12-preview.13
    - 0.0.12-preview.17
    - 0.0.12-preview.19
    - 0.0.12-preview.20
    - 1.0.0-preview.1
    - 1.0.1
    - 1.1.0-preview.1
    - 1.1.0
    - 1.2.1
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.3.1
    - 1.3.2
    compatible:
    - 1.3.2
    recommended: 1.3.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638409134840000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.mathematics@1.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Unity.Mathematics.git
    revision: 1695a8503482a3131be78cc26308a93f82c05b04
    path: 
  unityLifecycle:
    version: 1.3.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n* Fixed `math.hash` crash when using IL2CPP
    builds on Arm 32 bit devices.\n* Fixed obsolete method usage warnings for `MatrixDrawer.CanCacheInspectorGUI`
    and `PrimitiveVectorDrawer.CanCacheInspectorGUI` in UNITY_2023_2_OR_NEWER.\n*
    Updated minimum editor version to 2021.3"}'
  assetStore:
    productId: 
  fingerprint: 8017b507cc74bf0a1dd14b18aa860569f807314d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.3.2
    minimumUnityVersion: 2021.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.searcher@4.9.3
  testable: 0
  isDirectDependency: 0
  version: 4.9.3
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.searcher@1e17ce91558d
  assetPath: Packages/com.unity.searcher
  name: com.unity.searcher
  displayName: Searcher
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: General search window for use in the Editor. First target use is for
    GraphView node search.
  errors: []
  versions:
    all:
    - 4.0.0-preview
    - 4.0.0
    - 4.0.7-preview
    - 4.0.7
    - 4.0.8-preview
    - 4.0.9
    - 4.1.0-preview
    - 4.1.0
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 4.3.2
    - 4.6.0-preview
    - 4.7.0-preview
    - 4.9.1
    - 4.9.2
    - 4.9.3
    compatible:
    - 4.9.2
    - 4.9.3
    recommended: 4.9.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - search
  - searcher
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638731478077990000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.searcher@4.9/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git
    revision: 6fad693b6604ae7175b59ebb4990d9a0b6c1d012
    path: 
  unityLifecycle:
    version: 4.9.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed a bug where spaces are removed when highlighted.\n-
    Changed VisualSplitter to twoPaneSplitView and updated indentation"}'
  assetStore:
    productId: 
  fingerprint: 1e17ce91558d1d9127554adc03d275f39a7466a2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 4.9.2
    minimumUnityVersion: 2019.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrain
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.rendering.light-transport@1.0.1
  testable: 0
  isDirectDependency: 0
  version: 1.0.1
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.rendering.light-transport@9bd588f963c0
  assetPath: Packages/com.unity.rendering.light-transport
  name: com.unity.rendering.light-transport
  displayName: Unity Light Transport Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Unity Light Transport Library exposes reusable code for writing light
    transport algorithms such as raytracing or pathtracing
  errors: []
  versions:
    all:
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.collections
    version: 2.2.0
  - name: com.unity.mathematics
    version: 1.2.4
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords:
  - raytracing
  - pathtracing
  - monte-carlo
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 9bd588f963c0ca6105f0978f9cfc66444032a804
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.1
    minimumUnityVersion: 2023.3.0b1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.hierarchycore@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.hierarchycore
  assetPath: Packages/com.unity.modules.hierarchycore
  name: com.unity.modules.hierarchycore
  displayName: Hierarchy Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'Module that contains a high-performance hierarchy container. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.HierarchyCoreModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.ext.nunit@2.0.5
  testable: 0
  isDirectDependency: 0
  version: 2.0.5
  source: 2
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.ext.nunit@031a54704bff
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: 
  description: A custom version of NUnit used by Unity Test Framework. Based on NUnit
    version 3.5 and works with all platforms, il2cpp and Mono AOT.
  errors: []
  versions:
    all:
    - 2.0.5
    compatible:
    - 2.0.5
    recommended: 2.0.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ext.nunit@2.0/manual/index.html
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 031a54704bffe39e6a0324909f8eaa4565bdebf2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.5
    minimumUnityVersion: 2019.4.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.2d.common@9.0.7
  testable: 0
  isDirectDependency: 0
  version: 9.0.7
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.2d.common@bb1fc9b3d81b
  assetPath: Packages/com.unity.2d.common
  name: com.unity.2d.common
  displayName: 2D Common
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Common is a package that contains shared functionalities that are
    used by most of the other 2D packages.
  errors: []
  versions:
    all:
    - 1.0.9-preview.1
    - 1.0.9-preview.2
    - 1.0.10-preview
    - 1.0.11-preview.1
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.2.0-preview.1
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.1
    - 2.1.2
    - 3.0.0
    - 3.0.1
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.0.4
    - 4.1.0
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 6.0.0-pre.2
    - 6.0.0-pre.3
    - 6.0.0-pre.4
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.1.0
    - 7.0.0-pre.3
    - 7.0.0-pre.4
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 8.0.0-pre.1
    - 8.0.0-pre.2
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.1.0
    - 8.1.1
    - 9.0.0-pre.1
    - 9.0.0-pre.2
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.0.6
    - 9.0.7
    - 9.1.0
    - 9.1.1
    - 10.0.0
    - 11.0.0
    - 11.0.1
    compatible:
    - 9.0.7
    - 9.1.0
    - 9.1.1
    recommended: 9.0.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.4
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.21
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459039420000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.common@9.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: c393ad93bdba3e78ebd30a5ccd2e6da5d8b92aba
    path: 
  unityLifecycle:
    version: 9.0.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- DANB-638 Fixed Error \"InvalidOperationException:
    HTTP/1.1 404 Not Found\" logged when entering Play Mode in 2D Common Sample Scene\n-
    DANB-637 Fixed Sprite Atlases included in the 2D Common Package Sample \"Sprite
    Atlas Samples\" are blurry even though they are uncompressed"}'
  assetStore:
    productId: 
  fingerprint: bb1fc9b3d81b3bb452c6708e8c088fe4224a0369
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 9.0.7
    minimumUnityVersion: 2023.1.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: /Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.tilemap
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
- packageId: com.unity.test-framework.performance@3.1.0
  testable: 0
  isDirectDependency: 0
  version: 3.1.0
  source: 1
  resolvedPath: /Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed
  assetPath: Packages/com.unity.test-framework.performance
  name: com.unity.test-framework.performance
  displayName: Performance testing API
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Package that extends Unity Test Framework package. Adds performance
    testing capabilities and collects configuration metadata.
  errors: []
  versions:
    all:
    - 0.1.27-preview
    - 0.1.29-preview
    - 0.1.31-preview
    - 0.1.33-preview
    - 0.1.34-preview
    - 0.1.36-preview
    - 0.1.37-preview
    - 0.1.39-preview
    - 0.1.40-preview
    - 0.1.41-preview
    - 0.1.42-preview
    - 0.1.44-preview
    - 0.1.45-preview
    - 0.1.47-preview
    - 0.1.48-preview
    - 0.1.49-preview
    - 0.1.50-preview
    - 1.0.4-preview
    - 1.0.6-preview
    - 1.0.9-preview
    - 1.1.2-preview
    - 1.2.0-preview
    - 1.2.1-preview
    - 1.2.3-preview
    - 1.2.5-preview
    - 1.2.6-preview
    - 1.3.0-preview
    - 1.3.1-preview
    - 1.3.2-preview
    - 1.3.3-preview
    - 2.0.1-preview
    - 2.0.2-preview
    - 2.0.3-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 2.0.9-preview
    - 2.1.0-preview
    - 2.2.0-preview
    - 2.3.1-preview
    - 2.4.1-preview
    - 2.5.1-preview
    - 2.6.0-preview
    - 2.7.0-preview
    - 2.8.0-preview
    - 2.8.1-preview
    - 3.0.0-pre.1
    - 3.0.0-pre.2
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.1.0
    compatible:
    - 3.1.0
    recommended: 3.1.0
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.33
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - performance
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
    compliance:
      status: 0
      violations: []
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638792643567670000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.test-framework.performance@3.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.test-framework.performance.git
    revision: 6126c0ee357019ed762100ffeca520029274e869
    path: 
  unityLifecycle:
    version: 3.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Added an optional command-line argument
    \"perfTestResults\" to control the target location of performance test run results
    file.\n### Fixed\n- Warmup cycles no longer record GC measurements.\n- Setup
    and Cleanup cycles no longer contribute to GC measurements."}'
  assetStore:
    productId: 
  fingerprint: 92d1d09a72ed696fa23fd76c675b29d211664b50
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.1.0
    minimumUnityVersion: 2020.3.0a1
  compliance:
    status: 0
    violation:
      scopePatternExpression: 
      message: 
      readMoreLink: 
m_BuiltInPackagesHash: 261425ba29191de55c933548bf6364bb6add9f8c
