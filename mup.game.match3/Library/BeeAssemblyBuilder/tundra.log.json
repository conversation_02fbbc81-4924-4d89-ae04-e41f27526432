{"msg":"init","dagFile":"Library/BeeAssemblyBuilder/2AB.dag","targets":["ScriptAssemblies"]}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"ScriptAssemblies","enqueuedNodeIndex":7}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll","enqueuedNodeIndex":5,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":7}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)","enqueuedNodeIndex":4,"enqueueingNodeAnnotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll","enqueueingNodeIndex":5}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.UnityAdditionalFile.txt","enqueuedNodeIndex":1,"enqueueingNodeAnnotation":"Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp","enqueuedNodeIndex":2,"enqueueingNodeAnnotation":"Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2","enqueuedNodeIndex":3,"enqueueingNodeAnnotation":"Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.pdb","enqueuedNodeIndex":6,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":7}
{"msg":"newNode","annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2","index":3}
{"msg":"runNodeAction","annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2","displayName":"Writing Assembly-CSharp.rsp2","index":3}
{"msg":"noderesult","processed_node_count":0,"number_of_nodes_ever_queued":7,"annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2","index":3,"exitcode":0,"outputfile":"Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2"}
{"msg":"newNode","annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.UnityAdditionalFile.txt","index":1}
{"msg":"runNodeAction","annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.UnityAdditionalFile.txt","displayName":"Writing Assembly-CSharp.UnityAdditionalFile.txt","index":1}
{"msg":"newNode","annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp","index":2}
{"msg":"runNodeAction","annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp","displayName":"Writing Assembly-CSharp.rsp","index":2}
{"msg":"noderesult","processed_node_count":1,"number_of_nodes_ever_queued":7,"annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.UnityAdditionalFile.txt","index":1,"exitcode":0,"outputfile":"Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.UnityAdditionalFile.txt"}
{"msg":"noderesult","processed_node_count":2,"number_of_nodes_ever_queued":7,"annotation":"WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp","index":2,"exitcode":0,"outputfile":"Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp"}
{"msg":"newNode","annotation":"Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)","index":4}
{"msg":"runNodeAction","annotation":"Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)","displayName":"Compiling C# (Assembly-CSharp)","index":4}
{"msg":"noderesult","processed_node_count":3,"number_of_nodes_ever_queued":7,"annotation":"Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)","index":4,"exitcode":0,"outputfile":"Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll","stdout":"Assets/_Assets/MUP/Core/MUPSingleton.cs(7,9): warning UDR0001: No method with [RuntimeInitializeOnLoadMethod] attribute"}
{"msg":"newNode","annotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.pdb","index":6}
{"msg":"runNodeAction","annotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.pdb","displayName":"Copying Assembly-CSharp.pdb","index":6}
{"msg":"newNode","annotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll","index":5}
{"msg":"runNodeAction","annotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll","displayName":"Copying Assembly-CSharp.dll","index":5}
{"msg":"noderesult","processed_node_count":4,"number_of_nodes_ever_queued":7,"annotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.pdb","index":6,"exitcode":0,"outputfile":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.pdb"}
{"msg":"noderesult","processed_node_count":5,"number_of_nodes_ever_queued":7,"annotation":"CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll","index":5,"exitcode":0,"outputfile":"Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll"}
{"msg":"newNode","annotation":"ScriptAssemblies","index":7}
{"msg":"runNodeAction","annotation":"ScriptAssemblies","index":7}
