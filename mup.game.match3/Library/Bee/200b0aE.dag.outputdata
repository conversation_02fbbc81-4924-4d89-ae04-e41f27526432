{"Bee.Core.BuildProgramContext+BuildProgramContextOutputData": {"MaxRerunAllowed": 2147483647}, "ScriptCompilationBuildProgram.Data.ScriptCompilationData_Out": {"Assemblies": [{"Path": "Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Domain_Reload.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Domain_Reload.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Domain_Reload.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/NuGetForUnity.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/NuGetForUnity.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/NuGetForUnity.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/R3.Unity.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/R3.Unity.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/R3.Unity.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UniTask.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UniTask.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UniTask.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.Utils.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.Utils.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.Utils.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/R3.Unity.TextMeshPro.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/R3.Unity.TextMeshPro.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/R3.Unity.TextMeshPro.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UniTask.Linq.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UniTask.Linq.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UniTask.Linq.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UniTask.TextMeshPro.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UniTask.TextMeshPro.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ResourceManager.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ResourceManager.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ResourceManager.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/LitMotion.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/LitMotion.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/LitMotion.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/LitMotion.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/LitMotion.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/LitMotion.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/UniTask.Addressables.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/UniTask.Addressables.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/UniTask.Addressables.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/LitMotion.Extensions.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/LitMotion.Extensions.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/LitMotion.Extensions.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.Framework.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.Framework.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.Framework.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}, {"Path": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll", "ScriptUpdaterRsp": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp", "MovedFromExtractorFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}], "LocalizeCompilerMessages": false}}