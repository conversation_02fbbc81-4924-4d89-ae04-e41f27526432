{"name": "Unity.ProjectAuditor.Editor.UI", "rootNamespace": "", "references": ["Unity.ProjectAuditor.Editor", "Unity.ProjectAuditor.Editor.UI.Framework", "Unity.ProjectAuditor.Editor.Utils"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.build-report-inspector", "expression": "", "define": "PACKAGE_BUILD_REPORT_INSPECTOR"}, {"name": "Unity", "expression": "[2020.3,2023.2)", "define": "PA_OLD_ANALYTICS_API"}], "noEngineReferences": false}