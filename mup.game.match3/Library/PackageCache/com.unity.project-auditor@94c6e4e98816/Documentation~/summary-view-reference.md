# Summary view reference

After completing a [project analysis](analyze-project.md) or loading a Report, Project Auditor displays the Summary View.

This view displays a high level overview of the issues that Project Auditor found.

![](images/summary-view.png)<br/>_Project Auditor Summary view_

## Issue Breakdown

Displays the total number of issues grouped by their severity for the Code, Assets, and Project Settings area of your project.

## Top Ten Issues

Displays a list of 10 priority issues in your project. Select the issue to view more details of the issue and navigate to its relevant view in the Project Auditor window.

## Additional Insights

Contains links to the Build Size view and the Compiled Assemblies view. Also displays compiler errors if there are any in your project.

## Session Information

Displays functional information such as the creation date and time of the report, the name and version number of the project, and the Unity version and the Project Auditor package version which was used.

## Additional resources

* [Analyze your project](analyze-project.md)
* [Project issues](project-issues.md)