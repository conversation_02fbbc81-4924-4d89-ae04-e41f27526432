{"Nodes": [{"Annotation": "all_tundra_nodes", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9], "DebugActionIndex": 0}, {"Annotation": "WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.UnityAdditionalFile.txt", "DisplayName": "Writing Assembly-CSharp.UnityAdditionalFile.txt", "ActionType": "WriteFile", "PayloadOffset": 110, "PayloadLength": 58, "PayloadDebugContentSnippet": "/Users/<USER>/Desktop/Project", "Inputs": [], "InputFlags": [], "Outputs": ["Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.UnityAdditionalFile.txt"], "OutputFlags": [2], "DebugActionIndex": 1}, {"Annotation": "WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp", "DisplayName": "Writing Assembly-CSharp.rsp", "ActionType": "WriteFile", "PayloadOffset": 264, "PayloadLength": 33495, "PayloadDebugContentSnippet": "-target:library\n-out:\"Library/", "Inputs": [], "InputFlags": [], "Outputs": ["Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp"], "OutputFlags": [2], "DebugActionIndex": 2}, {"Annotation": "WriteText Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2", "DisplayName": "Writing Assembly-CSharp.rsp2", "ActionType": "WriteFile", "PayloadOffset": 33856, "PayloadLength": 0, "PayloadDebugContentSnippet": "", "Inputs": [], "InputFlags": [], "Outputs": ["Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2"], "OutputFlags": [2], "DebugActionIndex": 3}, {"Annotation": "Csc Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll (+2 others)", "DisplayName": "Compiling C# (Assembly-CSharp)", "Action": "\"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetCoreRuntime/dotnet\" exec \"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp\" \"@Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp2\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll", "Assets/Packages/Microsoft.Bcl.AsyncInterfaces.6.0.0/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "Assets/Packages/Microsoft.Bcl.TimeProvider.8.0.0/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll", "Assets/Packages/R3.1.3.0/lib/netstandard2.1/R3.dll", "Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll", "Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "Assets/Packages/System.Threading.Channels.8.0.0/lib/netstandard2.1/System.Threading.Channels.dll", "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.Unsafe.dll", "Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll", "Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/AOT/Newtonsoft.Json.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Domain_Reload.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/LitMotion.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/LitMotion.Extensions.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/R3.Unity.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/R3.Unity.TextMeshPro.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.Addressables.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.DOTween.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.Linq.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.TextMeshPro.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.Animation.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.Common.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.IK.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.PixelPerfect.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.SpriteShape.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.Tilemap.Extras.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Addressables.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Burst.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Collections.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.InputSystem.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.InputSystem.ForUI.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.InternalAPIEngineBridge.001.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Mathematics.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Profiling.Core.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Rendering.LightTransport.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Core.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Core.Runtime.Shared.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Core.ShaderLibrary.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.GPUDriven.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.2D.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.Config.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.Shaders.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.ResourceManager.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.ScriptableBuildPipeline.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.TextMeshPro.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UnityEngine.UI.dll", "Assets/Test.cs", "Assets/_Assets/MUP/Core/MUPSingleton.cs", "Assets/_Assets/MUP/Core/PoolManager.cs", "Assets/_Assets/MUP/Feel/FeelManager.cs", "Assets/_Assets/MUP/SO/AudioSO.cs", "Assets/_Assets/MUP/SO/PoolSO.cs", "Assets/_Assets/MUP/Sound/MUPSfxObject.cs", "Assets/_Assets/MUP/Sound/SoundManager.cs", "Assets/_Assets/MUP/UI/MUPView.cs", "Assets/_Assets/MUP/UI/UIManager.cs", "Assets/_Assets/MUP/Utils/Constant.cs", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll", "Library/PackageCache/com.unity.project-auditor@94c6e4e98816/RoslynAnalyzers/Domain_Reload_Analyzer.dll", "Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], "Outputs": ["Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll", "Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.pdb", "Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.ref.dll"], "OutputFlags": [0, 0, 0], "ToBuildDependencies": [1, 2, 3], "ToUseDependencies": [1, 3], "AllowUnexpectedOutput": true, "Env": [{"Key": "DOTNET_MULTILEVEL_LOOKUP", "Value": "0"}], "DebugActionIndex": 4}, {"Annotation": "CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll", "DisplayName": "Copying Assembly-CSharp.dll", "ActionType": "CopyFiles", "Inputs": ["Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.dll"], "InputFlags": [0], "Outputs": ["Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.dll"], "OutputFlags": [2], "ToBuildDependencies": [4], "DebugActionIndex": 5}, {"Annotation": "CopyFiles Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.pdb", "DisplayName": "Copying Assembly-CSharp.pdb", "ActionType": "CopyFiles", "Inputs": ["Library/BeeAssemblyBuilder/artifacts/2AB.dag/Assembly-CSharp.pdb"], "InputFlags": [0], "Outputs": ["Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Assembly-CSharp.pdb"], "OutputFlags": [2], "ToBuildDependencies": [4], "DebugActionIndex": 6}, {"Annotation": "ScriptAssemblies", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [5, 6], "DebugActionIndex": 7}, {"Annotation": "BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json", "DisplayName": "Extracting script serialization layouts", "Action": "\"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/Microsoft.Bcl.AsyncInterfaces.6.0.0/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/Microsoft.Bcl.TimeProvider.8.0.0/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/R3.1.3.0/lib/netstandard2.1/R3.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/System.Threading.Channels.8.0.0/lib/netstandard2.1/System.Threading.Channels.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Assembly-CSharp.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Domain_Reload.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/LitMotion.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/LitMotion.Extensions.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/R3.Unity.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/R3.Unity.TextMeshPro.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/R3.Unity.XRInteractionToolkit.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/UniTask.Addressables.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/UniTask.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/UniTask.DOTween.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/UniTask.Linq.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/UniTask.TextMeshPro.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.Addressables.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.Burst.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.Collections.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.InputSystem.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.Mathematics.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.PerformanceTesting.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.Profiling.Core.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.ResourceManager.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/Unity.TextMeshPro.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/UnityEngine.TestRunner.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies/UnityEngine.UI.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.Unsafe.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Mdb.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Pdb.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Rocks.dll\" -a=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll\" -s=\"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine\" -s=\"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx\" -s=\"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard\" -s=\"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/EditorExtensions\" -s=\"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/Extensions/2.0.0\" -s=\"/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/ref/2.1.0\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/Microsoft.Bcl.AsyncInterfaces.6.0.0/lib/netstandard2.1\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/Microsoft.Bcl.TimeProvider.8.0.0/lib/netstandard2.0\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/R3.1.3.0/lib/netstandard2.1\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Assets/Packages/System.Threading.Channels.8.0.0/lib/netstandard2.1\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/ScriptAssemblies\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f\" -s=\"/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime\" -o=\"Library/BuildPlayerData/Editor\" -rn=\"\" -tn=\"TypeDb-All.json\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/netcorerun/netcorerun", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe", "Assets/Packages/Microsoft.Bcl.AsyncInterfaces.6.0.0/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "Assets/Packages/Microsoft.Bcl.TimeProvider.8.0.0/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll", "Assets/Packages/R3.1.3.0/lib/netstandard2.1/R3.dll", "Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll", "Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "Assets/Packages/System.Threading.Channels.8.0.0/lib/netstandard2.1/System.Threading.Channels.dll", "Library/ScriptAssemblies/Assembly-CSharp.dll", "Library/ScriptAssemblies/Domain_Reload.dll", "Library/ScriptAssemblies/LitMotion.dll", "Library/ScriptAssemblies/LitMotion.Extensions.dll", "Library/ScriptAssemblies/R3.Unity.dll", "Library/ScriptAssemblies/R3.Unity.TextMeshPro.dll", "Library/ScriptAssemblies/R3.Unity.XRInteractionToolkit.dll", "Library/ScriptAssemblies/UniTask.Addressables.dll", "Library/ScriptAssemblies/UniTask.dll", "Library/ScriptAssemblies/UniTask.DOTween.dll", "Library/ScriptAssemblies/UniTask.Linq.dll", "Library/ScriptAssemblies/UniTask.TextMeshPro.dll", "Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll", "Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll", "Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll", "Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll", "Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll", "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll", "Library/ScriptAssemblies/Unity.Addressables.dll", "Library/ScriptAssemblies/Unity.Burst.dll", "Library/ScriptAssemblies/Unity.Collections.dll", "Library/ScriptAssemblies/Unity.InputSystem.dll", "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll", "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll", "Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll", "Library/ScriptAssemblies/Unity.Mathematics.dll", "Library/ScriptAssemblies/Unity.PerformanceTesting.dll", "Library/ScriptAssemblies/Unity.Profiling.Core.dll", "Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll", "Library/ScriptAssemblies/Unity.ResourceManager.dll", "Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll", "Library/ScriptAssemblies/Unity.TextMeshPro.dll", "Library/ScriptAssemblies/UnityEngine.TestRunner.dll", "Library/ScriptAssemblies/UnityEngine.UI.dll", "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll", "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll", "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll", "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll", "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.Unsafe.dll", "Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll", "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll", "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Mdb.dll", "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Pdb.dll", "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Rocks.dll", "Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Outputs": ["Library/BuildPlayerData/Editor/TypeDb-All.json"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 8}, {"Annotation": "ScriptAssembliesAndTypeDB", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [7, 8], "DebugActionIndex": 9}], "FileSignatures": [{"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.BuildTools.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Core.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.CSharpSupport.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.DotNet.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.NativeProgramSupport.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Stevedore.Program.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.TinyProfiler2.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Toolchain.GNU.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Toolchain.LLVM.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Toolchain.VisualStudio.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Toolchain.Xcode.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Tools.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.TundraBackend.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Bee.VisualStudioSolution.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/BeeBuildProgramCommon.Data.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/BeeBuildProgramCommon.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Newtonsoft.Json.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/NiceIO.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.Data.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/ScriptCompilationBuildProgram.Data.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/SharpYaml.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Api.Attributes.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Cecil.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Cecil.Mdb.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Cecil.Pdb.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Cecil.Rocks.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.IL2CPP.Api.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.IL2CPP.Bee.BuildLogic.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Linker.Api.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Options.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline/UnityEditor.iOS.Extensions.Xcode.dll"}, {"File": "Library/BeeAssemblyBuilder/2AB-inputdata.json"}], "StatSignatures": [{"File": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/getconf"}, {"File": "Assets/csc.rsp"}, {"File": "Assets/mcs.rsp"}, {"File": "Assets/Packages/Microsoft.Bcl.AsyncInterfaces.6.0.0/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll"}, {"File": "Assets/Packages/Microsoft.Bcl.TimeProvider.8.0.0/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll"}, {"File": "Assets/Packages/R3.1.3.0/lib/netstandard2.1/R3.dll"}, {"File": "Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll"}, {"File": "Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll"}, {"File": "Assets/Packages/System.Threading.Channels.8.0.0/lib/netstandard2.1/System.Threading.Channels.dll"}, {"File": "Library/BeeAssemblyBuilder/2AB-inputdata.json"}, {"File": "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll"}, {"File": "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll"}, {"File": "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll"}, {"File": "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll"}, {"File": "Library/PackageCache/com.unity.burst@59eb6f11d242/Unity.Burst.Unsafe.dll"}, {"File": "Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"}, {"File": "Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"}, {"File": "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"}, {"File": "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Mdb.dll"}, {"File": "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Pdb.dll"}, {"File": "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.Rocks.dll"}, {"File": "Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"}, {"File": "Library/ScriptAssemblies/Assembly-CSharp.dll"}, {"File": "Library/ScriptAssemblies/Domain_Reload.dll"}, {"File": "Library/ScriptAssemblies/LitMotion.dll"}, {"File": "Library/ScriptAssemblies/LitMotion.Extensions.dll"}, {"File": "Library/ScriptAssemblies/R3.Unity.dll"}, {"File": "Library/ScriptAssemblies/R3.Unity.TextMeshPro.dll"}, {"File": "Library/ScriptAssemblies/R3.Unity.XRInteractionToolkit.dll"}, {"File": "Library/ScriptAssemblies/UniTask.Addressables.dll"}, {"File": "Library/ScriptAssemblies/UniTask.dll"}, {"File": "Library/ScriptAssemblies/UniTask.DOTween.dll"}, {"File": "Library/ScriptAssemblies/UniTask.Linq.dll"}, {"File": "Library/ScriptAssemblies/UniTask.TextMeshPro.dll"}, {"File": "Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll"}, {"File": "Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll"}, {"File": "Library/ScriptAssemblies/Unity.Addressables.dll"}, {"File": "Library/ScriptAssemblies/Unity.Burst.dll"}, {"File": "Library/ScriptAssemblies/Unity.Collections.dll"}, {"File": "Library/ScriptAssemblies/Unity.InputSystem.dll"}, {"File": "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}, {"File": "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}, {"File": "Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll"}, {"File": "Library/ScriptAssemblies/Unity.Mathematics.dll"}, {"File": "Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}, {"File": "Library/ScriptAssemblies/Unity.Profiling.Core.dll"}, {"File": "Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}, {"File": "Library/ScriptAssemblies/Unity.ResourceManager.dll"}, {"File": "Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll"}, {"File": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}, {"File": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}, {"File": "Library/ScriptAssemblies/UnityEngine.UI.dll"}], "GlobSignatures": [{"Path": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline"}], "ContentDigestExtensions": [".rsp", ".dll", ".exe", ".pdb", ".json", ".c<PERSON><PERSON>j"], "StructuredLogFileName": "Library/BeeAssemblyBuilder/tundra.log.json", "StateFileName": "Library/BeeAssemblyBuilder/TundraBuildState.state", "StateFileNameTmp": "Library/BeeAssemblyBuilder/TundraBuildState.state.tmp", "StateFileNameMapped": "Library/BeeAssemblyBuilder/TundraBuildState.state.map", "ScanCacheFileName": "Library/BeeAssemblyBuilder/tundra.scancache", "ScanCacheFileNameTmp": "Library/BeeAssemblyBuilder/tundra.scancache.tmp", "DigestCacheFileName": "Library/BeeAssemblyBuilder/tundra.digestcache", "DigestCacheFileNameTmp": "Library/BeeAssemblyBuilder/tundra.digestcache.tmp", "CachedNodeOutputDirectoryName": "Library/BeeAssemblyBuilder/CachedNodeOutput", "EmitDataForBeeWhy": 0, "NamedNodes": {"all_tundra_nodes": 0, "ScriptAssemblies": 7, "ScriptAssembliesAndTypeDB": 9}, "DefaultNodes": [0], "SharedResources": [], "Scanners": [], "Identifier": "Library/BeeAssemblyBuilder/2AB.dag.json", "PayloadsFile": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3/Library/BeeAssemblyBuilder/2AB.dag.payloads", "RelativePathToRoot": "../.."}