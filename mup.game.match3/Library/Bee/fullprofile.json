{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 52256, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 52256, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 52256, "tid": 170002, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 52256, "tid": 170002, "ts": 1753521603851995, "dur": 479, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 52256, "tid": 170002, "ts": 1753521603855467, "dur": 694, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 52256, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 52256, "tid": 1, "ts": 1753521603406840, "dur": 6861, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 52256, "tid": 1, "ts": 1753521603413704, "dur": 59702, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 52256, "tid": 1, "ts": 1753521603473414, "dur": 44615, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 52256, "tid": 170002, "ts": 1753521603856175, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 52256, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603405049, "dur": 4774, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603409825, "dur": 435670, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603410587, "dur": 3222, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603413812, "dur": 818, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603414632, "dur": 15259, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603429897, "dur": 365, "ph": "X", "name": "ProcessMessages 8178", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448299, "dur": 115, "ph": "X", "name": "ReadAsync 8178", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448423, "dur": 4, "ph": "X", "name": "ProcessMessages 8168", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448429, "dur": 72, "ph": "X", "name": "ReadAsync 8168", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448513, "dur": 1, "ph": "X", "name": "ProcessMessages 1396", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448515, "dur": 142, "ph": "X", "name": "ReadAsync 1396", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448662, "dur": 2, "ph": "X", "name": "ProcessMessages 3292", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448690, "dur": 55, "ph": "X", "name": "ReadAsync 3292", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448798, "dur": 2, "ph": "X", "name": "ProcessMessages 2611", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448800, "dur": 90, "ph": "X", "name": "ReadAsync 2611", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448892, "dur": 1, "ph": "X", "name": "ProcessMessages 2671", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448894, "dur": 69, "ph": "X", "name": "ReadAsync 2671", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448966, "dur": 1, "ph": "X", "name": "ProcessMessages 2989", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603448968, "dur": 70, "ph": "X", "name": "ReadAsync 2989", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603449039, "dur": 1, "ph": "X", "name": "ProcessMessages 2135", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603449041, "dur": 947, "ph": "X", "name": "ReadAsync 2135", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450028, "dur": 22, "ph": "X", "name": "ProcessMessages 8124", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450073, "dur": 163, "ph": "X", "name": "ReadAsync 8124", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450243, "dur": 2, "ph": "X", "name": "ProcessMessages 3398", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450246, "dur": 48, "ph": "X", "name": "ReadAsync 3398", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450296, "dur": 1, "ph": "X", "name": "ProcessMessages 1840", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450297, "dur": 284, "ph": "X", "name": "ReadAsync 1840", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450584, "dur": 1, "ph": "X", "name": "ProcessMessages 1030", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450586, "dur": 34, "ph": "X", "name": "ReadAsync 1030", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450621, "dur": 20, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450643, "dur": 36, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450680, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450681, "dur": 115, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450797, "dur": 1, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450799, "dur": 177, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450979, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603450980, "dur": 66, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451058, "dur": 14, "ph": "X", "name": "ProcessMessages 1724", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451074, "dur": 84, "ph": "X", "name": "ReadAsync 1724", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451159, "dur": 2, "ph": "X", "name": "ProcessMessages 2489", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451163, "dur": 25, "ph": "X", "name": "ReadAsync 2489", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451199, "dur": 124, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451325, "dur": 1, "ph": "X", "name": "ProcessMessages 1395", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451328, "dur": 203, "ph": "X", "name": "ReadAsync 1395", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451540, "dur": 69, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451616, "dur": 1, "ph": "X", "name": "ProcessMessages 1839", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451618, "dur": 23, "ph": "X", "name": "ReadAsync 1839", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451650, "dur": 34, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451687, "dur": 1, "ph": "X", "name": "ProcessMessages 1264", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451689, "dur": 31, "ph": "X", "name": "ReadAsync 1264", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451739, "dur": 65, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451806, "dur": 39, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451846, "dur": 5, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451852, "dur": 46, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603451901, "dur": 45, "ph": "X", "name": "ReadAsync 1027", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452063, "dur": 100, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452164, "dur": 2, "ph": "X", "name": "ProcessMessages 3448", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452200, "dur": 25, "ph": "X", "name": "ReadAsync 3448", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452226, "dur": 1, "ph": "X", "name": "ProcessMessages 2304", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452228, "dur": 14, "ph": "X", "name": "ReadAsync 2304", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452244, "dur": 48, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452295, "dur": 78, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452374, "dur": 1, "ph": "X", "name": "ProcessMessages 2640", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452380, "dur": 69, "ph": "X", "name": "ReadAsync 2640", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452449, "dur": 1, "ph": "X", "name": "ProcessMessages 1770", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452454, "dur": 29, "ph": "X", "name": "ReadAsync 1770", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452491, "dur": 48, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452541, "dur": 1, "ph": "X", "name": "ProcessMessages 1564", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452543, "dur": 188, "ph": "X", "name": "ReadAsync 1564", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452731, "dur": 2, "ph": "X", "name": "ProcessMessages 5181", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452738, "dur": 33, "ph": "X", "name": "ReadAsync 5181", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452777, "dur": 46, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452824, "dur": 6, "ph": "X", "name": "ProcessMessages 1418", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452834, "dur": 27, "ph": "X", "name": "ReadAsync 1418", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452863, "dur": 55, "ph": "X", "name": "ReadAsync 1227", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452928, "dur": 1, "ph": "X", "name": "ProcessMessages 1310", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603452930, "dur": 82, "ph": "X", "name": "ReadAsync 1310", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453039, "dur": 1, "ph": "X", "name": "ProcessMessages 2529", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453085, "dur": 39, "ph": "X", "name": "ReadAsync 2529", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453125, "dur": 1, "ph": "X", "name": "ProcessMessages 1626", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453195, "dur": 23, "ph": "X", "name": "ReadAsync 1626", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453297, "dur": 62, "ph": "X", "name": "ProcessMessages 2699", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453448, "dur": 99, "ph": "X", "name": "ReadAsync 2699", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453553, "dur": 2, "ph": "X", "name": "ProcessMessages 7190", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453556, "dur": 23, "ph": "X", "name": "ReadAsync 7190", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453583, "dur": 1, "ph": "X", "name": "ProcessMessages 1699", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453585, "dur": 113, "ph": "X", "name": "ReadAsync 1699", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453699, "dur": 1, "ph": "X", "name": "ProcessMessages 2220", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453701, "dur": 63, "ph": "X", "name": "ReadAsync 2220", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453766, "dur": 1, "ph": "X", "name": "ProcessMessages 2033", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453772, "dur": 61, "ph": "X", "name": "ReadAsync 2033", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453834, "dur": 1, "ph": "X", "name": "ProcessMessages 2316", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453836, "dur": 37, "ph": "X", "name": "ReadAsync 2316", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603453881, "dur": 260, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454145, "dur": 1, "ph": "X", "name": "ProcessMessages 2868", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454153, "dur": 44, "ph": "X", "name": "ReadAsync 2868", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454198, "dur": 1, "ph": "X", "name": "ProcessMessages 1212", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454200, "dur": 78, "ph": "X", "name": "ReadAsync 1212", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454279, "dur": 1, "ph": "X", "name": "ProcessMessages 1828", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454280, "dur": 18, "ph": "X", "name": "ReadAsync 1828", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454300, "dur": 33, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454335, "dur": 48, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454386, "dur": 1, "ph": "X", "name": "ProcessMessages 1240", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454388, "dur": 26, "ph": "X", "name": "ReadAsync 1240", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454415, "dur": 1, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454417, "dur": 21, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454442, "dur": 3, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454445, "dur": 31, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454487, "dur": 25, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454521, "dur": 1, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454523, "dur": 35, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454560, "dur": 40, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454601, "dur": 1, "ph": "X", "name": "ProcessMessages 1208", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454603, "dur": 37, "ph": "X", "name": "ReadAsync 1208", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454642, "dur": 39, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454685, "dur": 43, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454730, "dur": 38, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454770, "dur": 25, "ph": "X", "name": "ReadAsync 1392", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454829, "dur": 40, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454871, "dur": 5, "ph": "X", "name": "ProcessMessages 1794", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454877, "dur": 23, "ph": "X", "name": "ReadAsync 1794", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454902, "dur": 62, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454967, "dur": 1, "ph": "X", "name": "ProcessMessages 1564", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603454969, "dur": 68, "ph": "X", "name": "ReadAsync 1564", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455037, "dur": 1, "ph": "X", "name": "ProcessMessages 1705", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455039, "dur": 28, "ph": "X", "name": "ReadAsync 1705", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455069, "dur": 139, "ph": "X", "name": "ReadAsync 1191", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455210, "dur": 2, "ph": "X", "name": "ProcessMessages 3369", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455213, "dur": 45, "ph": "X", "name": "ReadAsync 3369", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455264, "dur": 15, "ph": "X", "name": "ProcessMessages 1369", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455280, "dur": 35, "ph": "X", "name": "ReadAsync 1369", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455317, "dur": 41, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455368, "dur": 78, "ph": "X", "name": "ReadAsync 1401", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455448, "dur": 1, "ph": "X", "name": "ProcessMessages 2259", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455450, "dur": 89, "ph": "X", "name": "ReadAsync 2259", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455554, "dur": 1, "ph": "X", "name": "ProcessMessages 2382", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455556, "dur": 34, "ph": "X", "name": "ReadAsync 2382", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455627, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455632, "dur": 162, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455799, "dur": 1, "ph": "X", "name": "ProcessMessages 2375", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603455801, "dur": 190, "ph": "X", "name": "ReadAsync 2375", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456016, "dur": 4, "ph": "X", "name": "ProcessMessages 7871", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456021, "dur": 21, "ph": "X", "name": "ReadAsync 7871", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456043, "dur": 1, "ph": "X", "name": "ProcessMessages 1361", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456045, "dur": 43, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456090, "dur": 1, "ph": "X", "name": "ProcessMessages 1248", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456093, "dur": 93, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456187, "dur": 1, "ph": "X", "name": "ProcessMessages 2372", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456188, "dur": 23, "ph": "X", "name": "ReadAsync 2372", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456216, "dur": 5, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456222, "dur": 42, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456266, "dur": 45, "ph": "X", "name": "ReadAsync 1645", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456319, "dur": 15, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456336, "dur": 21, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456364, "dur": 33, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456401, "dur": 31, "ph": "X", "name": "ReadAsync 1344", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456433, "dur": 39, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456472, "dur": 33, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456575, "dur": 1, "ph": "X", "name": "ProcessMessages 1906", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456578, "dur": 39, "ph": "X", "name": "ReadAsync 1906", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456622, "dur": 5, "ph": "X", "name": "ProcessMessages 2467", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456634, "dur": 40, "ph": "X", "name": "ReadAsync 2467", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456675, "dur": 1, "ph": "X", "name": "ProcessMessages 1218", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456680, "dur": 23, "ph": "X", "name": "ReadAsync 1218", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456705, "dur": 15, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456721, "dur": 3, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456725, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456743, "dur": 27, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456773, "dur": 12, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456786, "dur": 39, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456826, "dur": 1, "ph": "X", "name": "ProcessMessages 1223", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456827, "dur": 23, "ph": "X", "name": "ReadAsync 1223", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603456854, "dur": 201, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603457157, "dur": 24, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603457182, "dur": 21, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603457204, "dur": 490, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603457695, "dur": 1, "ph": "X", "name": "ProcessMessages 986", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603457697, "dur": 96, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603457795, "dur": 1, "ph": "X", "name": "ProcessMessages 1255", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603457835, "dur": 1045, "ph": "X", "name": "ReadAsync 1255", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603458886, "dur": 2, "ph": "X", "name": "ProcessMessages 2641", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603458889, "dur": 86, "ph": "X", "name": "ReadAsync 2641", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603458976, "dur": 7, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603458984, "dur": 20, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603459007, "dur": 30, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603459039, "dur": 1, "ph": "X", "name": "ProcessMessages 51", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603459041, "dur": 491, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603459699, "dur": 1, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603459701, "dur": 183, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603459885, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603459886, "dur": 19968, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603479862, "dur": 5, "ph": "X", "name": "ProcessMessages 8185", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603479868, "dur": 49, "ph": "X", "name": "ReadAsync 8185", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603479921, "dur": 355, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603480287, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603480289, "dur": 364, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603480655, "dur": 274, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603480932, "dur": 209, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603481145, "dur": 240, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603481388, "dur": 155, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603481544, "dur": 47, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603481604, "dur": 130, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603481735, "dur": 142, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603481887, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603481908, "dur": 79, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603481995, "dur": 153, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482151, "dur": 78, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482231, "dur": 177, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482410, "dur": 179, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482591, "dur": 98, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482691, "dur": 27, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482720, "dur": 45, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482767, "dur": 197, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482966, "dur": 25, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603482993, "dur": 145, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483142, "dur": 15, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483159, "dur": 24, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483185, "dur": 28, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483215, "dur": 128, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483345, "dur": 26, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483376, "dur": 21, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483401, "dur": 31, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483434, "dur": 185, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483621, "dur": 14, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483636, "dur": 35, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483674, "dur": 29, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483705, "dur": 190, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483897, "dur": 13, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483912, "dur": 31, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483945, "dur": 30, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603483977, "dur": 190, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484174, "dur": 14, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484192, "dur": 15, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484216, "dur": 26, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484245, "dur": 233, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484480, "dur": 25, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484507, "dur": 34, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484544, "dur": 184, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484730, "dur": 153, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484885, "dur": 21, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484909, "dur": 35, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484946, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603484967, "dur": 36, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603485005, "dur": 12, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603485019, "dur": 185, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603485206, "dur": 152, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603485360, "dur": 154, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603485516, "dur": 258, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603485776, "dur": 14, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603485793, "dur": 19, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603485814, "dur": 206, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486022, "dur": 15, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486049, "dur": 166, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486217, "dur": 23, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486247, "dur": 13, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486263, "dur": 32, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486297, "dur": 197, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486496, "dur": 24, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486522, "dur": 186, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486710, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486732, "dur": 25, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486758, "dur": 37, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486796, "dur": 1, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603486797, "dur": 279, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487078, "dur": 96, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487176, "dur": 168, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487346, "dur": 96, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487444, "dur": 25, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487470, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487475, "dur": 121, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487597, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487599, "dur": 20, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487621, "dur": 18, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487641, "dur": 27, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487672, "dur": 146, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487821, "dur": 133, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603487956, "dur": 75, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488033, "dur": 183, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488218, "dur": 179, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488400, "dur": 30, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488433, "dur": 28, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488462, "dur": 45, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488509, "dur": 38, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488549, "dur": 167, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488719, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488734, "dur": 180, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603488916, "dur": 152, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489075, "dur": 147, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489224, "dur": 33, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489259, "dur": 149, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489411, "dur": 135, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489548, "dur": 33, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489583, "dur": 279, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489863, "dur": 5, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489869, "dur": 16, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603489887, "dur": 171, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490060, "dur": 30, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490091, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490093, "dur": 36, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490131, "dur": 187, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490321, "dur": 138, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490461, "dur": 24, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490487, "dur": 13, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490502, "dur": 33, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490537, "dur": 102, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490640, "dur": 13, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490661, "dur": 118, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603490781, "dur": 224, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491007, "dur": 17, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491033, "dur": 280, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491321, "dur": 16, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491339, "dur": 160, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491501, "dur": 164, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491667, "dur": 90, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491779, "dur": 22, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491802, "dur": 56, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603491860, "dur": 153, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492025, "dur": 132, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492159, "dur": 144, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492306, "dur": 148, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492456, "dur": 15, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492473, "dur": 23, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492498, "dur": 16, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492516, "dur": 58, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492576, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492600, "dur": 151, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492752, "dur": 14, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492768, "dur": 183, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603492954, "dur": 157, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493113, "dur": 15, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493130, "dur": 27, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493158, "dur": 142, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493302, "dur": 134, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493438, "dur": 138, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493577, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493601, "dur": 181, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493785, "dur": 26, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493819, "dur": 143, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603493964, "dur": 162, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494128, "dur": 109, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494240, "dur": 133, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494374, "dur": 43, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494418, "dur": 42, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494462, "dur": 144, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494608, "dur": 138, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494747, "dur": 50, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494800, "dur": 145, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603494946, "dur": 127, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603495075, "dur": 15, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603495093, "dur": 270, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603495365, "dur": 14, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603495381, "dur": 156, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603495539, "dur": 26, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603495567, "dur": 155, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603495724, "dur": 156, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603495881, "dur": 150, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496034, "dur": 127, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496162, "dur": 48, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496212, "dur": 16, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496230, "dur": 9, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496240, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496257, "dur": 21, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496280, "dur": 30, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496311, "dur": 182, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496496, "dur": 167, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496664, "dur": 146, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496813, "dur": 16, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496875, "dur": 15, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496891, "dur": 1, "ph": "X", "name": "ProcessMessages 1321", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496893, "dur": 14, "ph": "X", "name": "ReadAsync 1321", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603496908, "dur": 327, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497237, "dur": 76, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497314, "dur": 379, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497694, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497744, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497746, "dur": 56, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497809, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497832, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497918, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603497951, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498028, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498086, "dur": 18, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498105, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498160, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498272, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498302, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498339, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498384, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498461, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498481, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498505, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498579, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498581, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498628, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498647, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498679, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498712, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498766, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498839, "dur": 63, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498905, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498909, "dur": 73, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603498994, "dur": 87, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603499084, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603499131, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603499174, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603499337, "dur": 249, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603499607, "dur": 948, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603500650, "dur": 8, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603500660, "dur": 114, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603500777, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603500782, "dur": 1270, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603511969, "dur": 56, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603512036, "dur": 190, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603512228, "dur": 16, "ph": "X", "name": "ProcessMessages 5408", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603512244, "dur": 3786, "ph": "X", "name": "ReadAsync 5408", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603516035, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603516037, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603516204, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603516303, "dur": 3034, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603519340, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603519341, "dur": 7778, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603527135, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603527138, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603527282, "dur": 736, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603528021, "dur": 286, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603528310, "dur": 149, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603528461, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603528654, "dur": 656, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603529312, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603529434, "dur": 1230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603530666, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603530819, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603530844, "dur": 306, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603531152, "dur": 250, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603531405, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603531500, "dur": 526, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603532039, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603532157, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603532186, "dur": 1486, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603533675, "dur": 828, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603534515, "dur": 624, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603535160, "dur": 335, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603535534, "dur": 479, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603536065, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603536456, "dur": 308, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603536765, "dur": 1308, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603538078, "dur": 3040, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603541122, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603541124, "dur": 165, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603541291, "dur": 271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603541564, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603541686, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603541799, "dur": 362, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603542163, "dur": 716, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603542890, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603542921, "dur": 898, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603543822, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603543962, "dur": 688, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603544652, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603544717, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603544853, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603544958, "dur": 342, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603545302, "dur": 605, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603545913, "dur": 210, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603546124, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603546369, "dur": 1377, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603547748, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603547824, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603547916, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603548006, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603548012, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603548112, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603548189, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603548250, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603548335, "dur": 955, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603549292, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603549355, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603549508, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603549714, "dur": 291, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603550008, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603550039, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603550217, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603550407, "dur": 542, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603550951, "dur": 254, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603551207, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603551333, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603551440, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603551569, "dur": 248, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603551819, "dur": 233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603552055, "dur": 359, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603552415, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603552474, "dur": 338, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603552814, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603552923, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603552998, "dur": 660, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603553660, "dur": 371, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603554035, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603554157, "dur": 788, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603554954, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603555139, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603555155, "dur": 769, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603555925, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603555927, "dur": 411, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603556340, "dur": 235, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603556577, "dur": 499, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603557078, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603557192, "dur": 302, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603557496, "dur": 403, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603557902, "dur": 226, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603558130, "dur": 207, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603558339, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603558381, "dur": 630, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603559013, "dur": 304, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603559320, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603559459, "dur": 28, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603559489, "dur": 371, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603559862, "dur": 571, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603560436, "dur": 240, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603560679, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603560859, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603561089, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603561194, "dur": 369, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603561565, "dur": 212, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603561780, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603561878, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603561955, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603562062, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603562179, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603562255, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603562277, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603562386, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603562435, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603562513, "dur": 653, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603563168, "dur": 611, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603563780, "dur": 158, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603563940, "dur": 510, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603564452, "dur": 1658, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603566117, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603566121, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603566204, "dur": 191, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603566398, "dur": 69, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603566469, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603566679, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603566750, "dur": 497, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603567249, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603567450, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603567612, "dur": 333, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603567952, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603567959, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603568090, "dur": 367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603568458, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603568460, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603568728, "dur": 300, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603569030, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603569120, "dur": 255, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603569378, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603569504, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603569661, "dur": 382, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603570045, "dur": 407, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603570454, "dur": 263, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603570721, "dur": 495, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603571219, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603571296, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603571326, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603571367, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603571399, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603571514, "dur": 303, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603571819, "dur": 228, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603572048, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603572049, "dur": 613, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603572664, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603572817, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603572844, "dur": 404, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603573251, "dur": 236, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603573489, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603573682, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603573743, "dur": 293, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603574038, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603574138, "dur": 384, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603574525, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603574529, "dur": 78521, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653061, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653067, "dur": 66, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653138, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653140, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653208, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653266, "dur": 50, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653318, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653359, "dur": 13, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653379, "dur": 30, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603653411, "dur": 1496, "ph": "X", "name": "ProcessMessages 2550", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603654913, "dur": 2190, "ph": "X", "name": "ReadAsync 2550", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603657124, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603657130, "dur": 1917, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603659051, "dur": 1376, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603660430, "dur": 74, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603660511, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603660515, "dur": 246, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603660766, "dur": 753, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603661521, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603661525, "dur": 1767, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603663297, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603663300, "dur": 1761, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603665064, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603665066, "dur": 1952, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603667026, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603667031, "dur": 193, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603667227, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603667229, "dur": 643, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603667884, "dur": 166, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603668052, "dur": 1253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603669312, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603669315, "dur": 1095, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603670414, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603670819, "dur": 979, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603671802, "dur": 1195, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603673001, "dur": 1123, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603674127, "dur": 1718, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603675849, "dur": 368, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603676220, "dur": 671, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603676898, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603676902, "dur": 1079, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603677983, "dur": 986, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603678973, "dur": 898, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603679874, "dur": 801, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603680685, "dur": 1136, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603681823, "dur": 983, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603682808, "dur": 740, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603683678, "dur": 1018, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603684699, "dur": 1350, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603686052, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603686064, "dur": 3777, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603689853, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603689860, "dur": 5559, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603695425, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603695428, "dur": 2244, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603697683, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603697689, "dur": 894, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603698587, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603698591, "dur": 1111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603699708, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603699711, "dur": 1363, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603701080, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603701082, "dur": 494, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603701579, "dur": 754, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603703556, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603703559, "dur": 1133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603704697, "dur": 886, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603705587, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603705629, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603705638, "dur": 1710, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603707356, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603707360, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603707543, "dur": 274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603707820, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603707822, "dur": 858, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603708683, "dur": 638, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603709325, "dur": 237, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603709566, "dur": 33, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603709603, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603709693, "dur": 395, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603710091, "dur": 185, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603710279, "dur": 101, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603710382, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603710457, "dur": 231, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603710701, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603710778, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603710923, "dur": 188, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603711114, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603711244, "dur": 171, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603711417, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603711604, "dur": 142, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603711747, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603711890, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603711927, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603711997, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603712082, "dur": 76, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603712160, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603712196, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603712328, "dur": 542, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603712871, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603712874, "dur": 147, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603713023, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603713147, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603713151, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603713325, "dur": 123, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603713450, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603713530, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603713699, "dur": 168, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603713869, "dur": 145, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714016, "dur": 145, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714163, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714271, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714418, "dur": 103, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714523, "dur": 150, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714675, "dur": 151, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714828, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714879, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603714965, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603715064, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603715069, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603715216, "dur": 171, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603715390, "dur": 200, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603715592, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603715728, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603715896, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603715932, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716115, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716119, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716205, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716224, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716270, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716346, "dur": 89, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716438, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716441, "dur": 177, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716621, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716623, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716664, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716716, "dur": 275, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716994, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603716998, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717049, "dur": 5, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717054, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717094, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717229, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717304, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717455, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717482, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717554, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717606, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717655, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717733, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717735, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717885, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603717890, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603718049, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603718074, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603718122, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603718186, "dur": 403, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603718592, "dur": 10063, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603728659, "dur": 21, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603728682, "dur": 2617, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603731300, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603731302, "dur": 106421, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603837728, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603837731, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603837777, "dur": 35, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603837816, "dur": 40, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603837858, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603837906, "dur": 29, "ph": "X", "name": "ReadAsync 4798", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603837937, "dur": 29, "ph": "X", "name": "ProcessMessages 1260", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603837966, "dur": 2730, "ph": "X", "name": "ReadAsync 1260", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603840698, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603840700, "dur": 268, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603840970, "dur": 29, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603841000, "dur": 158, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603841159, "dur": 220, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 52256, "tid": 12884901888, "ts": 1753521603841381, "dur": 3701, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 52256, "tid": 170002, "ts": 1753521603856188, "dur": 1196, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 52256, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 52256, "tid": 8589934592, "ts": 1753521603402625, "dur": 115420, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 52256, "tid": 8589934592, "ts": 1753521603518048, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 52256, "tid": 8589934592, "ts": 1753521603518058, "dur": 1791, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 52256, "tid": 170002, "ts": 1753521603857386, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 52256, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 52256, "tid": 4294967296, "ts": 1753521603359385, "dur": 486918, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 52256, "tid": 4294967296, "ts": 1753521603363013, "dur": 34910, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 52256, "tid": 4294967296, "ts": 1753521603846450, "dur": 3998, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 52256, "tid": 4294967296, "ts": 1753521603848192, "dur": 1396, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 52256, "tid": 4294967296, "ts": 1753521603850498, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 52256, "tid": 170002, "ts": 1753521603857396, "dur": 16, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753521603405665, "dur": 3253, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753521603408931, "dur": 20478, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753521603429492, "dur": 51, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753521603429543, "dur": 98, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753521603430137, "dur": 342, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_91118A2F728DAD9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753521603430554, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_39B25D32CFFE745B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753521603430720, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_8192A56F15586443.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753521603431103, "dur": 17830, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753521603450394, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_03EB462F708254BF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753521603465706, "dur": 14660, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753521603429646, "dur": 67909, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753521603497568, "dur": 343978, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753521603841634, "dur": 461, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753521603429604, "dur": 67971, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603497595, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753521603497794, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E6099293577F65EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603497995, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_8B7F52F4EF803EEA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603498236, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603498316, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FD99BC8C7952C75B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603498414, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603498512, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_58154A82C8AEEED2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603498612, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603498737, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_070DA0E44072E4AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603498819, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AEA8660476D6039F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603498913, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603498971, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_FF3C62C209E1F2AF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603499062, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603499206, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_97D4D89498459919.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603499284, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603499341, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5A2D3B4655B3286F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603499537, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D0B6A15278A42C4B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603499669, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603499798, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_54E2C669D74DB25A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603499891, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603500261, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603501091, "dur": 3928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603505073, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603505156, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603505229, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603505371, "dur": 10751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603516122, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603516434, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603516521, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603516630, "dur": 3047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603519678, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603519739, "dur": 7600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603527340, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603527507, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603527651, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603527723, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603528450, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603531023, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603531232, "dur": 1872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603533104, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603533176, "dur": 5147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603538323, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603538455, "dur": 2112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603540616, "dur": 3680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603544296, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603544398, "dur": 5256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603549655, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603549816, "dur": 1699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603551535, "dur": 6359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603557894, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603557961, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603559159, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603559229, "dur": 2008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603561237, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603561311, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603562249, "dur": 1311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/NuGetForUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603563593, "dur": 3568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/NuGetForUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603567161, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603567291, "dur": 1028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/R3.Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603568319, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603568430, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603568978, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753521603569503, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603569580, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603570973, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603571059, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603571702, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603571812, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603571872, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603572048, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603572292, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603572863, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603573151, "dur": 81982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603655133, "dur": 4905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603660039, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603660146, "dur": 3583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603663730, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603663932, "dur": 3331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.DOTween.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603667263, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603667337, "dur": 3719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603671057, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603671184, "dur": 4301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603675486, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603675584, "dur": 6585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603682170, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603682358, "dur": 5401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603687760, "dur": 1310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603689116, "dur": 12935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603702052, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603702160, "dur": 4216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603706414, "dur": 3167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753521603709582, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603709743, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603709798, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603710098, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603710260, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603710512, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753521603710691, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603710829, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603710935, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603711007, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603711117, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603711209, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603711342, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753521603711401, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603711601, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603711719, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/R3.Unity.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753521603711825, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603711901, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603711974, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753521603712035, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603712264, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603712351, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.DOTween.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753521603712409, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603712520, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603712797, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603712999, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603713153, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603713211, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603713350, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603713437, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603713507, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603713685, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Addressables.DocExampleCode.Editor.Tests.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603713739, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603713899, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603714101, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753521603714151, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603714255, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603714350, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603714574, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603714827, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603714978, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603715156, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603715266, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603715334, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProjectAuditor.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603715445, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603715600, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603715674, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603715802, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603715864, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603716032, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603716132, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603716196, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UniTask.Addressables.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603716270, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603716468, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603716656, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603716890, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603716983, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603717107, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603717172, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603717296, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603717408, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603717477, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603717612, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603717678, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603717738, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603717805, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603718011, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/R3.Unity.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603718073, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603718213, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753521603718365, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603718431, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753521603718775, "dur": 122733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603429605, "dur": 67992, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603497603, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753521603497869, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9FEDC77DF18D321C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603498200, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_355EDFCE7F3816F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603498326, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603498382, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E00800116238B81E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603498514, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603498577, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_AEBC23A8E4FA79A7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603498642, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603498698, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_0DC0DA8529CF1082.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603498822, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0462768E64EABD0A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603498973, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_7436A235A0073526.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603499108, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A6AABF0E6ADA0B25.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603499181, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603499244, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_9358C75F1190FC64.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603499334, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_293131879DA5DD14.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603499469, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603499545, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_A79420259383E98A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603499697, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603499784, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603499943, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603500015, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_45806E81BE62D86B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603500085, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_3FB038AB94CBD0EF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603500210, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603500312, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_6C262D80C93BD4F0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603500481, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603500590, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_395D75F92AC91BB0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603500756, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603500858, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603501149, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753521603501392, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753521603501565, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603502125, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753521603502490, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603502688, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603502744, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753521603502990, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603503114, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603503259, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1753521603503573, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753521603503896, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603504050, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603504181, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603504403, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603504532, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603504608, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603504707, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603505465, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ResourceManager.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603505562, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ResourceManager.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603505645, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1753521603505871, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603506169, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603506492, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603506687, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603506748, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603506850, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.Framework.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603507278, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.Framework.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603507612, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603507753, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603507841, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603507993, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603508108, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603508390, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603508479, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603508574, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/NuGetForUnity.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603508631, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603508692, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603508798, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603508868, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603508940, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603509001, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753521603509241, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603510278, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603511558, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603512316, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603513279, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603514147, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603514938, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603515772, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603516737, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603517672, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603518466, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603519514, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603520870, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603521651, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603522471, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603523375, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603524266, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603525023, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603525844, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603526653, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603527516, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603528337, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603528925, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603528978, "dur": 2779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603531757, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603531865, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Profiling.Core.ref.dll_A852EED04B1E1709.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603531965, "dur": 1169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603533134, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603533229, "dur": 5687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ResourceManager.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603538917, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603539012, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ResourceManager.ref.dll_5CB66F786D630BD3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603539068, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603539176, "dur": 1989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603541193, "dur": 3121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603544315, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603544422, "dur": 1439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603545892, "dur": 2237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603548129, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603548389, "dur": 1763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603550182, "dur": 3153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603553335, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603553455, "dur": 3789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603557245, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603557438, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603558615, "dur": 1244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603559860, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603559988, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603560106, "dur": 6254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603566361, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603566499, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603566612, "dur": 3372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603569985, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603570109, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603571196, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603571525, "dur": 1985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603573532, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603573609, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603574109, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603574186, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603574469, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753521603574562, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603575652, "dur": 153443, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603729786, "dur": 1201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603731533, "dur": 191, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603838097, "dur": 268, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753521603732148, "dur": 106242, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753521603841121, "dur": 313, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753521603429611, "dur": 67994, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603497609, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_A93D6DF8E051997E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603497914, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_9EFE93A4B6B28B73.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603498162, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_39B25D32CFFE745B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603498435, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603498531, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_6C9DA28BBAE3FB82.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603498621, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603498710, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D7B1BF987A36C198.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603498794, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603498847, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A07FB6BEB1E7333E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603498985, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8D8F81C2AD007FF4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603499072, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_65DFED6065744ECD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603499187, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_BA115901BA18BB2D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603499281, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603499367, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_013EC6C429305933.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603499490, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603499570, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A8529918C73873A7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603499729, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603499835, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753521603499949, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603500025, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753521603500207, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603500545, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603500634, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603501068, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753521603501207, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603501298, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603501609, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603502134, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753521603502497, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603502656, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603502882, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603502982, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603503126, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603503193, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603503640, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603503778, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603504006, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753521603504271, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603504395, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603504701, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603504797, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753521603504971, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603505035, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603505714, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603505834, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603506103, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603506292, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603506406, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753521603506677, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603507216, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603507298, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/LitMotion.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603507583, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603507672, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603507761, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603507853, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603507929, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603508074, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603508387, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603508458, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603508561, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603508699, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603508804, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603508942, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603509016, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753521603509237, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603509307, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603510332, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603511624, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603512359, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603513312, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603514187, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603514978, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603515799, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603516748, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603517667, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603518443, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603519497, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603520856, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603521617, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603522392, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603523261, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603524196, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603524928, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603525754, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603526579, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603527417, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603528295, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603528660, "dur": 3852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603532512, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603532658, "dur": 1452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603534151, "dur": 1344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603535495, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603535574, "dur": 5345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603540920, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603541058, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603542269, "dur": 3419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603545688, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603545822, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603545979, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603546717, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603546823, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603548423, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603548480, "dur": 3170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603551651, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603551784, "dur": 3621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/LitMotion.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603555405, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603555522, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603556368, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603556992, "dur": 1746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603558739, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603558874, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603559807, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603559941, "dur": 3969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603563910, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603564207, "dur": 2721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603566929, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603567099, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/LitMotion.Extensions.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603567417, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/LitMotion.Extensions.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603568677, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603568783, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603569188, "dur": 1333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603570521, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603570743, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753521603570814, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603570878, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603571491, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603571638, "dur": 1831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603573485, "dur": 81655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603655142, "dur": 4350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603659493, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603659585, "dur": 3469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603663055, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603663248, "dur": 4256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.Linq.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603667506, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603667606, "dur": 3668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603671275, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603671429, "dur": 4981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603676411, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603676518, "dur": 3222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603679741, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603679814, "dur": 6152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/R3.Unity.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603685967, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603686499, "dur": 12417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProjectAuditor.Editor.Utils.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603698918, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603699102, "dur": 4597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603703701, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603704030, "dur": 3124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603707155, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603707274, "dur": 11202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753521603718476, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753521603718593, "dur": 122933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603429617, "dur": 67993, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603497614, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_5651B02FF685D981.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603497924, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_D7331C31AE624D95.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603498289, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_B519677B2E750245.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603498371, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603498503, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_C268CA3985788B50.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603498629, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2598F284265933B9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603498778, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_12CDA413F762C33F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603498849, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603498980, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603499046, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_ED9F64302AB7CA36.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603499135, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_A3EA01C4850945EE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603499281, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_3E4ECE7E8BB35882.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603499402, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603499481, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_4782BF78D66FB2E5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603499565, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603499657, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_4574CE91C336EF7D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603499832, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603499909, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603500134, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603500225, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_32CA5EEFEC612C27.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603500324, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_A9C2D5B3A007F854.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603500462, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603500546, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_03EB462F708254BF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603500722, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603500843, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603500964, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753521603501178, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603501264, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603501345, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603502174, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603503471, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1753521603503584, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603503808, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603504036, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603504324, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603504453, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603504543, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603504612, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603504987, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603505096, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603505268, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603505411, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.Utils.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603505643, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603505716, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603505807, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603506056, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603506766, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603507241, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603507533, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603507880, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603508039, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603508168, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603508337, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1753521603508443, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603508539, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Domain_Reload.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603508599, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603508677, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603508775, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603508879, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603508956, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753521603509461, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603509533, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603510459, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603511745, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603512562, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603513482, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603514339, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603515145, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603515949, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603516905, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603517831, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603518738, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603519882, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603521059, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603521809, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603522689, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603523645, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603524461, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603525240, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603526093, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603526857, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603527881, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603528549, "dur": 14748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603543386, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603544285, "dur": 10109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603554394, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603554492, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603554611, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603555329, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603555386, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603558370, "dur": 943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603559374, "dur": 4605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603563979, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603564130, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603564233, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603564326, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603564892, "dur": 1504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603566397, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603566458, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603567704, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603567852, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/R3.Unity.XRInteractionToolkit.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603567971, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603568076, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603569140, "dur": 992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603570132, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603570246, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603570368, "dur": 1289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603571658, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603571833, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753521603572888, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603573144, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603573244, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603573321, "dur": 81797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603655141, "dur": 4183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603659367, "dur": 2498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603661866, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603661954, "dur": 3574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603665530, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603665632, "dur": 4486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/R3.Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603670172, "dur": 4034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603674207, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603674282, "dur": 2964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603677247, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603677381, "dur": 2873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProjectAuditor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603680255, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603680405, "dur": 3483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603683889, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603684086, "dur": 10291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603694377, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603694623, "dur": 6841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603701465, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603701624, "dur": 4379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603706005, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603706079, "dur": 11769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753521603717849, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603718056, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753521603718116, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603718244, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603718487, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603718551, "dur": 122550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753521603841145, "dur": 305, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603429623, "dur": 67993, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603497620, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C5C1DFA646678D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603497891, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_C5C1DFA646678D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603497943, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_5BD5A8CCC51B35E5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603498267, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_110B7A9752F5AD38.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603498328, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603498398, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_061233166775A3D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603498523, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603498579, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_2D77F886ADC51F6A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603498770, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C30E9AD099929D47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603498848, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603498977, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603499040, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_15FE89EB11B2B491.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603499147, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_C1F8E474D4FC94AE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603499319, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603499387, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_479F21D64DDF5A5C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603499506, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603499595, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_06C0A05E69B9BD23.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603499726, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603499808, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3D9BC3FD62E6039E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603499917, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603499997, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_2CC8B0A3337F4D14.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603500125, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603500238, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603500493, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_857F5E2A119BA0C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603500671, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603500769, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603501355, "dur": 1586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603502941, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603503057, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603503167, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603503241, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603503914, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603504012, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753521603504343, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603504468, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753521603504569, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603504645, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753521603504742, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603504845, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603505153, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603505281, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603505541, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.Utils.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603505728, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603506153, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UniTask.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603506426, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753521603506695, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603507005, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603507505, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1753521603507677, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603507763, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603507897, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603508002, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603508123, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603508456, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603508620, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603508682, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603508860, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753521603509196, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603509830, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603510754, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603511967, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603512887, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603513713, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603514554, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603515345, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603516172, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603517295, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603518082, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603519020, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603520670, "dur": 357, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1753521603521028, "dur": 2524, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1753521603523552, "dur": 615, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1753521603520324, "dur": 3843, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603524167, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603524886, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603525731, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603526549, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603527346, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603528235, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603528469, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603528562, "dur": 1126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603529688, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603529755, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEngineBridge.001.ref.dll_C5B5631EE4DDD44F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603529807, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603529885, "dur": 5421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.Utils.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603535307, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603535401, "dur": 4955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603540357, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603540568, "dur": 1553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603542146, "dur": 3531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603545677, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603545789, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603546020, "dur": 4341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603550361, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603550445, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_E9EA3AB45900FF1D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603550495, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603550609, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/LitMotion.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603550693, "dur": 1186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603551879, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603551974, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603552212, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603552561, "dur": 4047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603556608, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603556704, "dur": 2876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603559581, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603559694, "dur": 1864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603561558, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603561655, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/LitMotion.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603562755, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603562835, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603562936, "dur": 3841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Domain_Reload.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603566777, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603566893, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603567193, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603568055, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603568195, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.Framework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603568331, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.Framework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603569492, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603569711, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603569797, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603569892, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603570905, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603571129, "dur": 1163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603572292, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603572901, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603573542, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753521603573613, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603573924, "dur": 81215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603655139, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/R3.Unity.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603657836, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603657956, "dur": 2613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603660583, "dur": 2553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603663136, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603663215, "dur": 5268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603668485, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603668624, "dur": 4740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/LitMotion.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603673365, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603673464, "dur": 3219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603676684, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603676778, "dur": 4285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603681064, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603681159, "dur": 3955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/LitMotion.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603685115, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603685221, "dur": 11418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603696640, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603697389, "dur": 2629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProjectAuditor.Editor.UI.Framework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603700020, "dur": 1058, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603701090, "dur": 4748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603705838, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603705955, "dur": 4543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753521603710499, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603710706, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Addressables.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603710823, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603710893, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603710953, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603711039, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603711129, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603711376, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603711532, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603711631, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603711796, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/LitMotion.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603711853, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603712069, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603712132, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603712235, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603712612, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/NuGetForUnity.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603712664, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603712728, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603712838, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603712907, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603713088, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603713200, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603713307, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603713357, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603713536, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603713591, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603713649, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603713738, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603713947, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ProjectAuditor.Editor.Utils.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603714018, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603714255, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603714420, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603714477, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603714639, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/R3.Unity.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603714730, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603714871, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603715007, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603715065, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603715149, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603715209, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603715263, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603715404, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603715479, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603715553, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753521603715625, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603715891, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603715960, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603716057, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603716246, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603716326, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603716388, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603716509, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603716695, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603716846, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603716945, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603717117, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603717259, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603717319, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603717552, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603717636, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603717781, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603717855, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603717928, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603717987, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603718102, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603718166, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753521603718216, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603718419, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753521603718654, "dur": 122889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603429630, "dur": 68014, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603497647, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A137065F34CC9C45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603497919, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603497986, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_FDF1DF7383BE0CAD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603498284, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_4475AF2AFFABA926.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603498426, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603498523, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_1D0F8F342DF5CF0F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603498609, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603498721, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_CA0E1C7BC0C607E9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603498815, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_9179AE1968B95645.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603498950, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_462079EBF000A463.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603499103, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C59F701EDC7B3906.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603499165, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603499241, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_5A158695308CA418.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603499322, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603499423, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_EB44D0B764B7C8DC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603499528, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8F6AF5EA65F36C03.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603499653, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603499746, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8D21704F39F1057D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603499889, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603500006, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_C166689E8CA2A04E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603500103, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603500229, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603501213, "dur": 3233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603504447, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603504691, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603504789, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753521603504946, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753521603505057, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603505152, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603505243, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753521603505345, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603505503, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603505726, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603505882, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603506376, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603506706, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603506971, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603507320, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603507591, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603507704, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603507899, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603507973, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603508088, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603508157, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603508412, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603508524, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/LitMotion.Extensions.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603508588, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603508672, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603508786, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603508887, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603508999, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753521603509347, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603509401, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603510379, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603511652, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603512423, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603513367, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603514242, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603515030, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603515865, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603516851, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603517728, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603518570, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603519629, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603520941, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603521691, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603522508, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603523453, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603524311, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603525101, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603525898, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603526719, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603527695, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603528377, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603528484, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ProjectAuditor.Editor.Utils.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603528817, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603531513, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603531606, "dur": 1609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603533215, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603533267, "dur": 3455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603536722, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603536824, "dur": 5233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603542057, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603542178, "dur": 2865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603545043, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603545183, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603545929, "dur": 2556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603548485, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603548809, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/R3.Unity.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603549966, "dur": 1818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/R3.Unity.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603551784, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603551895, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603552428, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603553225, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603554112, "dur": 4272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603558384, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603558485, "dur": 2265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603560750, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603560853, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/R3.Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603561142, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603561985, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603562039, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603562910, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603562977, "dur": 3666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603566644, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603566750, "dur": 4445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603571196, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603571598, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_BF69964832659484.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603571778, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603571856, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753521603572541, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603573147, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603573282, "dur": 81854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603655139, "dur": 4242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603659423, "dur": 3931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603663354, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603663443, "dur": 4315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603667759, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603667912, "dur": 4315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603672227, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603672447, "dur": 2909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/R3.Unity.XRInteractionToolkit.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603675357, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603675439, "dur": 2987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603678427, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603678564, "dur": 4711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603683275, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603683381, "dur": 3426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProjectAuditor.Editor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603686808, "dur": 2275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603689084, "dur": 530, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ProjectAuditor.Editor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603689618, "dur": 6255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603695873, "dur": 1306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603697202, "dur": 5596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603702800, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603702897, "dur": 4882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603707781, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753521603707878, "dur": 10847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753521603718780, "dur": 122724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603429636, "dur": 68013, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603497653, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_661E9919313836F1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603497917, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0254B4EAD9FA5CB9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603498248, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C9234425C371F41A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603498351, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603498422, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_89BDB88F64678315.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603498563, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603498618, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_5A04CF1DC9DD1390.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603498838, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603498909, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A49295611C727860.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603498978, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603499044, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_165FE63856186A10.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603499142, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_A7772F47C16CAC56.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603499243, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603499309, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_E5822FB88B5F8525.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603499371, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603499454, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_0897B8A47BC70881.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603499547, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603499645, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_60DE2D62F663D764.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603499790, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603499914, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603499980, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_82FEB68469624F24.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603500190, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603500267, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_ED2AB5385077AD72.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603500470, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_8895869DB6F5BBF5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603500561, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603500717, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_BA6BF233C73D6014.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603500818, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603500929, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603502038, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753521603502666, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603503596, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603503720, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603503919, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603504068, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603504499, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603504629, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603504717, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603504918, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603505000, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603505126, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603505207, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603505518, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753521603505759, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603506138, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ScriptableBuildPipeline.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603506230, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Animation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753521603506286, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603506429, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Aseprite.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1753521603506534, "dur": 998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603507623, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/LitMotion.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603507720, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603507869, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603508008, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603508117, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603508403, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603508539, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603508705, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603508768, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603508880, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603508981, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753521603509229, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603510248, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603511502, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603512297, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603513256, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603514145, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603514926, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603515747, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603516492, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603516772, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603517695, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603518474, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603519541, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603520890, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603521647, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603522459, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603523352, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603524245, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603524984, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603525826, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603526633, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603527484, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603528356, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603528947, "dur": 3482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603532429, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603532596, "dur": 2904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603535501, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603535607, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603535689, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603536318, "dur": 3520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603539838, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603539951, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603541191, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603541959, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603542242, "dur": 2790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603545032, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603545108, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_A1CE29DB57A110D1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603545160, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603545257, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603546582, "dur": 3810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603550392, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603550507, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_DD9C61FBB7D63C03.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603550598, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603550762, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603550846, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603551343, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603551410, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603552054, "dur": 1292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603553365, "dur": 4203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603557568, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603557667, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603558584, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603558655, "dur": 2823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603561478, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603561553, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Domain_Reload.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603562467, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603562580, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603562761, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603562907, "dur": 4030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/LitMotion.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603566937, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603567123, "dur": 1335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603568458, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603568668, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/R3.Unity.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603569028, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603569228, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603569849, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603569978, "dur": 1999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603571978, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603572087, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753521603572299, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603572504, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603572840, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603572907, "dur": 1218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603574125, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753521603574232, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603574494, "dur": 80631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603655133, "dur": 2103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603657285, "dur": 4406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603661692, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603661776, "dur": 3762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603665538, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603665679, "dur": 2665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603668345, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603668440, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Domain_Reload.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603670806, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603670923, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Domain_Reload.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603671115, "dur": 3372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UniTask.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603674488, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603674591, "dur": 4270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603678862, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603679012, "dur": 3479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603682491, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603682598, "dur": 6979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603689578, "dur": 3572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603693169, "dur": 3894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603697063, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603697573, "dur": 4091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603701665, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603701801, "dur": 3166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603704968, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603705064, "dur": 3122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603708187, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753521603708303, "dur": 10741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753521603719078, "dur": 122433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603429642, "dur": 68012, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603497655, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5CDB3D0CA3CB09B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603497906, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_C4615A0A510AFA65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603498102, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_DB3537FAE77D4E90.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603498244, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_161CFFFA16B8213C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603498376, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0D7C2BFD403ED278.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603498512, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603498586, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_845B1BE9B09512AE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603498677, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_6DF747A371F03CBE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603498774, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603498827, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_06E5AC6350BD5612.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603498964, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_B15AC01DD6F6A05B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603499159, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603499238, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_875874FE279274C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603499320, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603499395, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0E99BC6E1964CACE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603499511, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603499618, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6799CBAE2582DA9C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603499860, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603499936, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603500590, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603500730, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603501107, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753521603501322, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603502022, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753521603502366, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603502475, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603502544, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753521603502869, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_11476E2D9783C34A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603503011, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603503158, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603503257, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603504098, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603504461, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603504552, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603504634, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Profiling.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753521603504977, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603505077, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603505164, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603505686, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753521603505883, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603506214, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603506323, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603506464, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.PixelPerfect.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753521603506640, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603507412, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1753521603507685, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603507792, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603507984, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603508075, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603508170, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603508401, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603508487, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603508652, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UniTask.DOTween.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603508772, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603508876, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603508953, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753521603509245, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603509370, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603510348, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603511640, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603512389, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603513354, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603514232, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603515021, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603515860, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603516823, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603517722, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603518577, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603519654, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603520975, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603521709, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603522536, "dur": 967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603523503, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603524363, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603525118, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603525931, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603526744, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603527757, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603528766, "dur": 4766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603533532, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603533765, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603534878, "dur": 6805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603541683, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603541771, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_CB2B1FA2A63FF8D9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603541828, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603541894, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753521603542548, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603545276, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603545396, "dur": 4171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1753521603549611, "dur": 2696, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603653351, "dur": 543, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603552523, "dur": 101414, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1753521603655119, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603657341, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603657543, "dur": 3560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603661104, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603661173, "dur": 3707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603664880, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603664987, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/LitMotion.Extensions.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603667761, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603667845, "dur": 4192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603672038, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603672358, "dur": 3938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603676296, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603676411, "dur": 4743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603681154, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603681241, "dur": 8615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603689857, "dur": 2593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603692451, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603692518, "dur": 9707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603702226, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603702313, "dur": 2805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603705119, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603705326, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/NuGetForUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603707996, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603708134, "dur": 10218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753521603718353, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603718477, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753521603719089, "dur": 122400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753521603843475, "dur": 1869, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 52256, "tid": 170002, "ts": 1753521603857755, "dur": 2539, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 52256, "tid": 170002, "ts": 1753521603860341, "dur": 1590, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 52256, "tid": 170002, "ts": 1753521603854170, "dur": 8414, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}