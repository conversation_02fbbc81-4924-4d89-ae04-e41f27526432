apiRules:
  - exclude:
      uidRegex: ^UnityEditor\.ProjectAuditor\.EditorTests$
      type: Namespace
  - exclude:
      uidRegex: ^Unity\.ProjectAuditor\.Editor\.CodeAnalysis$
      type: Namespace
  - exclude:
      uidRegex: ^Unity\.ProjectAuditor\.Editor\.Utils$
      type: Namespace
  - exclude:
      uidRegex: ^Unity\.ProjectAuditor\.Editor\.Tests$
      type: Namespace
  - exclude:
      uidRegex: ^Unity\.ProjectAuditor\.Editor\.Tests\.Common$
      type: Namespace
  - exclude:
      uidRegex: ^Unity\.ProjectAuditor\.Editor\.Internal$
      type: Namespace