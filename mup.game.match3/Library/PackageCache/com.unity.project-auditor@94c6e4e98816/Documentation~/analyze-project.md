# Analyze your project

Use the Project Auditor window to run an analysis on your project. You can customize how Project Auditor runs in the [Preferences window](project-auditor-settings-reference.md), or you can use the [scripting APIs](project-auditor-programming.md).

## Create a new report

To analyze your project and get a report, perform the following steps:

1. Open the Project Auditor window (**Window** &gt; **Analysis** &gt; **Project Auditor**).
1. Select **Start Analysis**. If the window is already populated with data, select **New Analysis** to reset the window.

Project Auditor then analyzes your project. The analysis might take a few minutes to complete, depending on the chosen configuration and how large the project is. Once the analysis completes, Project Auditor displays a [Summary View](summary-view-reference.md) of the report.

> [!NOTE]
> By default, Project Auditor runs all [Project Areas](project-auditor-window-reference.md#project-area-views). To customize this default behavior, you can enable and disable specific Project Areas in the Preferences window.

## Load a report

To load a previously saved report:

1. Open the Project Auditor window (**Window** &gt; **Analysis** &gt; **Project Auditor**).
1. Select the Load icon (square with arrow), and choose the .projectauditor file to load.

## Additional resources

* [Project Auditor window reference](project-auditor-window.md)
* [Project Auditor settings reference](project-auditor-settings-reference.md)