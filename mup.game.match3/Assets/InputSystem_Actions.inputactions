{"version": 1, "name": "InputSystem_Actions", "maps": [{"name": "Player", "id": "61d6af5f-67bb-4101-b6cc-15c9d7e69735", "actions": [{"name": "Move", "type": "Value", "id": "7d5dbaf0-8e0b-4348-abf9-8c6dcb69f97e", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Look", "type": "Value", "id": "290afcd2-7892-4958-b6b2-08d7586d96d2", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Attack", "type": "<PERSON><PERSON>", "id": "beec843b-7c33-4533-afda-b779f1e4dc93", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Interact", "type": "<PERSON><PERSON>", "id": "f2ab9ee8-e225-4c46-83e0-9f09a9ce09bb", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "Hold", "initialStateCheck": false}, {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "id": "4a91d991-e561-423d-866b-aae0e33423ab", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "9c9aae70-649b-4152-a87b-999ba8044862", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Previous", "type": "<PERSON><PERSON>", "id": "ccbb0e7e-45b9-4763-a994-a6c80293ff9c", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Next", "type": "<PERSON><PERSON>", "id": "168c9983-f252-4f48-a7a0-6647b0711162", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Sprint", "type": "<PERSON><PERSON>", "id": "7e6ff181-de63-4e88-baf6-de37da88658f", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "acd67e66-d1e0-4688-ba42-47a51b961390", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "WASD", "id": "bb726593-191f-4bd7-b8a8-067c2dff2195", "path": "Dpad", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "0c022641-4bd9-4670-92cf-ccc6b3f6b7f8", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "319599c1-1d94-4eac-b516-aff8e4c513ca", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "5799d55d-81c8-4228-978b-d3cf52b52f4c", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "c1835311-532c-4a35-a1b2-85510437bb41", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "2193ee3d-a248-4c88-8a83-f5333bb54fcc", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "61991826-b507-44b9-9ab1-b651d78ef7f8", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "d746a138-3ee3-4222-b754-b81e9b9dd181", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "821fde84-ec3e-42e7-b70a-42ba0d84733f", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "553317b6-6dd8-48d2-9804-dde41c113d20", "path": "<XRController>/{Primary2DAxis}", "interactions": "", "processors": "", "groups": "XR", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3341d12e-83a4-4ee9-92e3-2474a944c0c6", "path": "<Joystick>/stick", "interactions": "", "processors": "", "groups": "Joystick", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0f93deae-ff3f-4173-b2c0-c6529fd1fffe", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bae72d09-2123-4d07-8f7d-d1a8e697e09d", "path": "<Pointer>/delta", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse;Touch", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f7a739db-1ec1-4276-a89d-a3db8128096d", "path": "<Joystick>/{Hatswitch}", "interactions": "", "processors": "", "groups": "Joystick", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "dbc81109-5c06-43be-8f1f-4604d9109f86", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6987beb4-9401-43a2-8fe8-9fd9334311bd", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "20979431-bf66-4a0c-b17a-2b316bf4c567", "path": "<Touchscreen>/primaryTouch/tap", "interactions": "", "processors": "", "groups": ";Touch", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "66e616d5-6edf-4290-aa5d-dc0aded8ed00", "path": "<Joystick>/trigger", "interactions": "", "processors": "", "groups": "Joystick", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "84ab7572-6c88-43be-8c85-cc89d24136cc", "path": "<XRController>/{PrimaryAction}", "interactions": "", "processors": "", "groups": "XR", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "264931a2-8dfe-4bcf-8444-c5553c776c68", "path": "<Keyboard>/enter", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "72e72d0d-153a-4f63-be83-cb8ee0a6f8a3", "path": "<Keyboard>/2", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Next", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "19071cee-7242-41b9-a08e-07b1c690ffaa", "path": "<Gamepad>/dpad/right", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Next", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "be3b9619-a0ec-4f60-817c-5436afd8b110", "path": "<Keyboard>/leftShift", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4d460d1b-6ca5-42aa-b8cc-d7924163233c", "path": "<Gamepad>/leftStickPress", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "456da631-db8a-4e9b-8527-492457df08a7", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "167f5a91-9c3e-4022-928a-cf1d9209e20e", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e0eada6d-4fd7-47df-8722-05e12c4c096a", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "907b355c-1ca5-4c63-911d-580759969200", "path": "<XRController>/secondaryButton", "interactions": "", "processors": "", "groups": "XR", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3988122d-a6d3-4ee1-bebd-42bb88238d25", "path": "<Keyboard>/1", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Previous", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "90062fdd-4b2f-4dbb-ba68-a52e0c43c724", "path": "<Gamepad>/dpad/left", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Previous", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0d8bc05a-6f2c-43c8-852b-6eddc52b1edf", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6e7b5113-b771-481a-9259-b5fa8b1db322", "path": "<Gamepad>/buttonNorth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d88db334-0706-452d-8132-1b8065f25bd3", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "Gamepad", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c8a9c44c-571d-480b-970f-eb81e2a2f811", "path": "<Keyboard>/c", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}]}, {"name": "UI", "id": "56c69e3b-0bea-46d7-a9fb-8f1210b86e64", "actions": [{"name": "Navigate", "type": "PassThrough", "id": "b92bb9c0-2a42-4c64-84f7-f3a326fae99c", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "4a8777e1-c2a9-4d56-90d2-fd37aa9b5fa3", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "7c341a70-4686-42f2-918e-75e7ef6928e7", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Point", "type": "PassThrough", "id": "6032ba54-09c0-421e-a7f9-8ba012db6a76", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Click", "type": "PassThrough", "id": "22388548-db54-4ec7-bff4-a6c5e502c1d9", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "RightClick", "type": "PassThrough", "id": "c1b4da5b-58c3-4457-8bfc-a78d2070ad8e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MiddleClick", "type": "PassThrough", "id": "8ce6716d-e8a1-4623-a76d-2d9c3011f30a", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "ScrollWheel", "type": "PassThrough", "id": "e33c869e-ce58-4714-ae0a-e740d6955f9a", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDevicePosition", "type": "PassThrough", "id": "61ecae5e-1551-4c4f-a87d-41571ed15429", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDeviceOrientation", "type": "PassThrough", "id": "cb511086-2847-4435-9c5a-e3cbb990bcc8", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "Gamepad", "id": "b97577f4-6b0c-4b7f-ac67-5f768a12bc5a", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "d8a73801-1f99-4a01-867c-484ff605df0b", "path": "<Gamepad>/leftStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "3f4088af-3524-49fb-886e-31fc472eff0e", "path": "<Gamepad>/rightStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "f2621cc0-145e-4370-b739-a03cf7fcba9a", "path": "<Gamepad>/leftStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "18037378-3c7b-40ed-af02-d5d471da8413", "path": "<Gamepad>/rightStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "badf4073-1fd3-4c95-811d-5d1446498ad7", "path": "<Gamepad>/leftStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "377b3cc0-beae-48a5-962d-6301a77578d5", "path": "<Gamepad>/rightStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "8949be0f-d5ff-4810-a6a5-9253dae560b4", "path": "<Gamepad>/leftStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "7ad5dd55-915a-4735-a313-ee443f47ce16", "path": "<Gamepad>/rightStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "79a854bd-7d0e-4270-aaa7-f1348f3e029d", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "Joystick", "id": "7e20a17e-a67b-4503-9cb8-c92dee702cff", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "b3a78d9c-6762-4e72-82e1-2ba0090f93ba", "path": "<Joystick>/stick/up", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "dc71ea4d-91e0-4739-85b6-faba7f12d509", "path": "<Joystick>/stick/down", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "643a3e73-46ac-42e8-aec0-364a22fc81f1", "path": "<Joystick>/stick/left", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "2668170a-f56c-4c94-87c1-54d34f7a86ac", "path": "<Joystick>/stick/right", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "Keyboard", "id": "d2ec4fef-9cc4-4e79-9ef4-e0f36cbd184c", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "62137d5e-6f59-4111-8127-22b48212ab14", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "4f9ba875-2799-4534-ba43-facc0113fc7a", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "6c8f1dc9-aafc-4075-b4a9-cd7c380e69af", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "09121662-f135-4912-9ba3-5cc86deb217f", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "6f1f4638-6ca4-4344-9ce9-5699cd77c1f5", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "c706cc44-fdb2-4c39-a0a6-e86eed4945d4", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "a12a4c5e-4fe1-4a35-91a4-6c2baa8d754e", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "9919f45f-f426-4eb7-9687-7c7ae06c0600", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "4dc735dc-e64a-487c-a9f6-8a10d3e59882", "path": "*/{Submit}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Touch;Joystick;XR", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0225aa8e-2565-4779-a2f7-e6aa29357f11", "path": "*/{Cancel}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Touch;Joystick;XR", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2eaf5f3d-ffe3-4269-bb84-f0d9606bb80a", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "322a41b7-0116-4e4a-95ce-19d7ab326335", "path": "<Pen>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "aa0e9737-ca9e-4abe-83c1-6f3887e6959e", "path": "<Touchscreen>/touch*/position", "interactions": "", "processors": "", "groups": "Touch", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0cbc7e0f-b784-4454-82cc-7a65e04e4ca7", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8a36d04d-f05b-4ba2-879c-30182e52f640", "path": "<Pen>/tip", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "703eb663-bd4d-47fe-aedf-25a3d9906ac2", "path": "<Touchscreen>/touch*/press", "interactions": "", "processors": "", "groups": "Touch", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "87051c95-feaa-4363-8e11-539ebffca579", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4db6cfa8-a5e2-4fe3-9971-d654da4712c3", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "ScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8e7530ac-3c0e-4f4c-ab98-a17f619fcec4", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "RightClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0cd4987e-fc04-467e-b58e-d9c073c7f1e5", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "MiddleClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "28468793-3922-410e-99ae-ee34a217dbaf", "path": "<XRController>/devicePosition", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDevicePosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "906438e5-7a0d-403c-8572-4788b4927b5d", "path": "<XRController>/deviceRotation", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDeviceOrientation", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "Touch", "bindingGroup": "Touch", "devices": [{"devicePath": "<Touchscreen>", "isOptional": false, "isOR": false}]}, {"name": "Joystick", "bindingGroup": "Joystick", "devices": [{"devicePath": "<Joystick>", "isOptional": false, "isOR": false}]}, {"name": "XR", "bindingGroup": "XR", "devices": [{"devicePath": "<XRController>", "isOptional": false, "isOR": false}]}]}