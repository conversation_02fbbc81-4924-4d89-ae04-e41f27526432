Base path: '/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents', plugins path '/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=1784 file=Packages/com.unity.project-auditor/Editor/UI/Framework/ProjectAuditor.shader name=Unlit/ProjectAuditor pass=<Unnamed Pass 0> ppOnly=0 stripLineD=0 buildPlatform=2 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR UNITY_NEEDS_RENDERPASS_FBFETCH_FALLBACK uKW= dKW=UNITY_UI_ALPHACLIP UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=4294967328 lang=0 type=Vertex platform=metal reqs=33 mask=6 start=53 ok=1 outsize=2143

