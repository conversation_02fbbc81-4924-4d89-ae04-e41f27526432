{"BeeBuildProgramCommon.Data.ConfigurationData": {"Il2CppDir": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/il2cpp", "UnityLinkerPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/il2cpp/build/deploy/UnityLinker", "Il2CppPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/il2cpp/build/deploy/il2cpp", "NetCoreRunPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/netcorerun/netcorerun", "DotNetExe": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetCoreRuntime/dotnet", "EditorContentsPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents", "Packages": [{"Name": "com.annulusgames.lit-motion", "ResolvedPath": "Library/PackageCache/com.annulusgames.lit-motion@7688ea6127fb"}, {"Name": "com.cysharp.r3", "ResolvedPath": "Library/PackageCache/com.cysharp.r3@96346038d239"}, {"Name": "com.cysharp.unitask", "ResolvedPath": "Library/PackageCache/com.cysharp.unitask@86b6e6a2e286"}, {"Name": "com.github-glitchenzo.nugetforunity", "ResolvedPath": "Library/PackageCache/com.github-glitchenzo.nugetforunity@21b3bb0ec087"}, {"Name": "com.unity.addressables", "ResolvedPath": "Library/PackageCache/com.unity.addressables@2581a6be7421"}, {"Name": "com.unity.feature.2d", "ResolvedPath": "Library/PackageCache/com.unity.feature.2d@dd1ea8910f12"}, {"Name": "com.unity.ide.visualstudio", "ResolvedPath": "Library/PackageCache/com.unity.ide.visualstudio@198cdf337d13"}, {"Name": "com.unity.inputsystem", "ResolvedPath": "Library/PackageCache/com.unity.inputsystem@978211393e28"}, {"Name": "com.unity.project-auditor", "ResolvedPath": "Library/PackageCache/com.unity.project-auditor@94c6e4e98816"}, {"Name": "com.unity.render-pipelines.universal", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.universal@a55da47cc43f"}, {"Name": "com.unity.ugui", "ResolvedPath": "Library/PackageCache/com.unity.ugui@a0f5d16b3c82"}, {"Name": "com.unity.modules.accessibility", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.accessibility"}, {"Name": "com.unity.modules.androidjni", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.androidjni"}, {"Name": "com.unity.modules.animation", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.animation"}, {"Name": "com.unity.modules.assetbundle", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.assetbundle"}, {"Name": "com.unity.modules.audio", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.audio"}, {"Name": "com.unity.modules.imageconversion", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imageconversion"}, {"Name": "com.unity.modules.imgui", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imgui"}, {"Name": "com.unity.modules.jsonserialize", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.jsonserialize"}, {"Name": "com.unity.modules.particlesystem", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.particlesystem"}, {"Name": "com.unity.modules.physics", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics"}, {"Name": "com.unity.modules.physics2d", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics2d"}, {"Name": "com.unity.modules.screencapture", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.screencapture"}, {"Name": "com.unity.modules.ui", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.ui"}, {"Name": "com.unity.modules.unitywebrequest", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequest"}, {"Name": "com.unity.modules.unitywebrequestassetbundle", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestassetbundle"}, {"Name": "com.unity.modules.unitywebrequestaudio", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestaudio"}, {"Name": "com.unity.modules.unitywebrequesttexture", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequesttexture"}, {"Name": "com.unity.modules.unitywebrequestwww", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestwww"}, {"Name": "com.unity.render-pipelines.core", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.core@2be5e7224a10"}, {"Name": "com.unity.shadergraph", "ResolvedPath": "Library/PackageCache/com.unity.shadergraph@052d95cfe62a"}, {"Name": "com.unity.render-pipelines.universal-config", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.universal-config@dd206bf35d04"}, {"Name": "com.unity.nuget.mono-cecil", "ResolvedPath": "Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f"}, {"Name": "com.unity.nuget.newtonsoft-json", "ResolvedPath": "Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0"}, {"Name": "com.unity.modules.uielements", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.uielements"}, {"Name": "com.unity.test-framework", "ResolvedPath": "Library/PackageCache/com.unity.test-framework@dfdbd02f5918"}, {"Name": "com.unity.2d.animation", "ResolvedPath": "Library/PackageCache/com.unity.2d.animation@494a3b4e73a9"}, {"Name": "com.unity.2d.pixel-perfect", "ResolvedPath": "Library/PackageCache/com.unity.2d.pixel-perfect@e3ae982b672d"}, {"Name": "com.unity.2d.psdimporter", "ResolvedPath": "Library/PackageCache/com.unity.2d.psdimporter@676bae148e11"}, {"Name": "com.unity.2d.sprite", "ResolvedPath": "Library/PackageCache/com.unity.2d.sprite@a1146c20a947"}, {"Name": "com.unity.2d.spriteshape", "ResolvedPath": "Library/PackageCache/com.unity.2d.spriteshape@9e35352ae135"}, {"Name": "com.unity.2d.tilemap", "ResolvedPath": "Library/PackageCache/com.unity.2d.tilemap@91e7def251e0"}, {"Name": "com.unity.2d.tilemap.extras", "ResolvedPath": "Library/PackageCache/com.unity.2d.tilemap.extras@13634da7dbe0"}, {"Name": "com.unity.2d.aseprite", "ResolvedPath": "Library/PackageCache/com.unity.2d.aseprite@996e69f78764"}, {"Name": "com.unity.profiling.core", "ResolvedPath": "Library/PackageCache/com.unity.profiling.core@aac7b93912bc"}, {"Name": "com.unity.scriptablebuildpipeline", "ResolvedPath": "Library/PackageCache/com.unity.scriptablebuildpipeline@7e8a1cf5a47d"}, {"Name": "com.unity.burst", "ResolvedPath": "Library/PackageCache/com.unity.burst@59eb6f11d242"}, {"Name": "com.unity.collections", "ResolvedPath": "Library/PackageCache/com.unity.collections@56bff8827a7e"}, {"Name": "com.unity.mathematics", "ResolvedPath": "Library/PackageCache/com.unity.mathematics@8017b507cc74"}, {"Name": "com.unity.searcher", "ResolvedPath": "Library/PackageCache/com.unity.searcher@1e17ce91558d"}, {"Name": "com.unity.modules.terrain", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrain"}, {"Name": "com.unity.rendering.light-transport", "ResolvedPath": "Library/PackageCache/com.unity.rendering.light-transport@9bd588f963c0"}, {"Name": "com.unity.modules.hierarchycore", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.hierarchycore"}, {"Name": "com.unity.ext.nunit", "ResolvedPath": "Library/PackageCache/com.unity.ext.nunit@031a54704bff"}, {"Name": "com.unity.2d.common", "ResolvedPath": "Library/PackageCache/com.unity.2d.common@bb1fc9b3d81b"}, {"Name": "com.unity.modules.tilemap", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.tilemap"}, {"Name": "com.unity.test-framework.performance", "ResolvedPath": "Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed"}], "UnityVersion": "6000.0.51f1", "UnityVersionNumeric": {"Release": 6000, "Major": 0, "Minor": 51}, "Batchmode": false, "EmitDataForBeeWhy": false, "NamedPipeOrUnixSocket": "/tmp/ilpp.sock-5cef3d9735e6f2b8cab8f93aa77f0875"}, "ScriptCompilationBuildProgram.Data.ScriptCompilationData": {"Assemblies": [{"Name": "Assembly-CSharp", "SourceFiles": ["Assets/Test.cs", "Assets/_Assets/MUP/Core/MUPSingleton.cs", "Assets/_Assets/MUP/Core/PoolManager.cs", "Assets/_Assets/MUP/Feel/FeelManager.cs", "Assets/_Assets/MUP/SO/AudioSO.cs", "Assets/_Assets/MUP/SO/PoolSO.cs", "Assets/_Assets/MUP/Sound/MUPSfxObject.cs", "Assets/_Assets/MUP/Sound/SoundManager.cs", "Assets/_Assets/MUP/UI/MUPView.cs", "Assets/_Assets/MUP/UI/UIManager.cs", "Assets/_Assets/MUP/Utils/Constant.cs"], "Defines": ["UNITY_6000_0_51", "UNITY_6000_0", "UNITY_6000", "UNITY_5_3_OR_NEWER", "UNITY_5_4_OR_NEWER", "UNITY_5_5_OR_NEWER", "UNITY_5_6_OR_NEWER", "UNITY_2017_1_OR_NEWER", "UNITY_2017_2_OR_NEWER", "UNITY_2017_3_OR_NEWER", "UNITY_2017_4_OR_NEWER", "UNITY_2018_1_OR_NEWER", "UNITY_2018_2_OR_NEWER", "UNITY_2018_3_OR_NEWER", "UNITY_2018_4_OR_NEWER", "UNITY_2019_1_OR_NEWER", "UNITY_2019_2_OR_NEWER", "UNITY_2019_3_OR_NEWER", "UNITY_2019_4_OR_NEWER", "UNITY_2020_1_OR_NEWER", "UNITY_2020_2_OR_NEWER", "UNITY_2020_3_OR_NEWER", "UNITY_2021_1_OR_NEWER", "UNITY_2021_2_OR_NEWER", "UNITY_2021_3_OR_NEWER", "UNITY_2022_1_OR_NEWER", "UNITY_2022_2_OR_NEWER", "UNITY_2022_3_OR_NEWER", "UNITY_2023_1_OR_NEWER", "UNITY_2023_2_OR_NEWER", "UNITY_2023_3_OR_NEWER", "UNITY_6000_0_OR_NEWER", "PLATFORM_ARCH_64", "UNITY_64", "ENABLE_AR", "ENABLE_AUDIO", "ENABLE_CACHING", "ENABLE_CLOTH", "ENABLE_MICROPHONE", "ENABLE_MULTIPLE_DISPLAYS", "ENABLE_PHYSICS", "ENABLE_TEXTURE_STREAMING", "ENABLE_VIRTUALTEXTURING", "ENABLE_LZMA", "ENABLE_UNITYEVENTS", "ENABLE_VR", "ENABLE_WEBCAM", "ENABLE_UNITYWEBREQUEST", "ENABLE_WWW", "ENABLE_CLOUD_SERVICES", "ENABLE_CLOUD_SERVICES_ADS", "ENABLE_CLOUD_SERVICES_USE_WEBREQUEST", "ENABLE_CLOUD_SERVICES_CRASH_REPORTING", "ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING", "ENABLE_CLOUD_SERVICES_PURCHASING", "ENABLE_CLOUD_SERVICES_ANALYTICS", "ENABLE_CLOUD_SERVICES_BUILD", "ENABLE_EDITOR_GAME_SERVICES", "ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT", "ENABLE_CLOUD_LICENSE", "ENABLE_EDITOR_HUB_LICENSE", "ENABLE_WEBSOCKET_CLIENT", "ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API", "ENABLE_DIRECTOR_AUDIO", "ENABLE_DIRECTOR_TEXTURE", "ENABLE_MANAGED_JOBS", "ENABLE_MANAGED_TRANSFORM_JOBS", "ENABLE_MANAGED_ANIMATION_JOBS", "ENABLE_MANAGED_AUDIO_JOBS", "ENABLE_MANAGED_UNITYTLS", "INCLUDE_DYNAMIC_GI", "ENABLE_SCRIPTING_GC_WBARRIERS", "PLATFORM_SUPPORTS_MONO", "RENDER_SOFTWARE_CURSOR", "ENABLE_MARSHALLING_TESTS", "ENABLE_VIDEO", "ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK", "ENABLE_ACCELERATOR_CLIENT_DEBUGGING", "TEXTCORE_1_0_OR_NEWER", "EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED", "PLATFORM_STANDALONE_OSX", "PLATFORM_STANDALONE", "UNITY_STANDALONE_OSX", "UNITY_STANDALONE", "ENABLE_GAMECENTER", "ENABLE_RUNTIME_GI", "ENABLE_MOVIES", "ENABLE_NETWORK", "ENABLE_CRUNCH_TEXTURE_COMPRESSION", "ENABLE_CLUSTER_SYNC", "ENABLE_CLUSTERINPUT", "ENABLE_SPATIALTRACKING", "PLATFORM_HAS_CUSTOM_MUTEX", "PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP", "ENABLE_MONO", "NET_STANDARD_2_0", "NET_STANDARD", "NET_STANDARD_2_1", "NETSTANDARD", "NETSTANDARD2_1", "ENABLE_CUSTOM_RENDER_TEXTURE", "ENABLE_DIRECTOR", "ENABLE_LOCALIZATION", "ENABLE_SPRITES", "ENABLE_TERRAIN", "ENABLE_TILEMAP", "ENABLE_TIMELINE", "ENABLE_INPUT_SYSTEM", "ENABLE_LEGACY_INPUT_MANAGER", "TEXTCORE_FONT_ENGINE_1_5_OR_NEWER", "TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER", "CSHARP_7_OR_LATER", "CSHARP_7_3_OR_NEWER", "ENABLE_UNITY_COLLECTIONS_CHECKS"], "PrebuiltReferences": ["/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "Assets/Packages/R3.1.3.0/lib/netstandard2.1/R3.dll", "Packages/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "Packages/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll", "Assets/Packages/Microsoft.Bcl.AsyncInterfaces.6.0.0/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "Assets/Packages/Microsoft.Bcl.TimeProvider.8.0.0/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll", "Assets/Packages/System.Threading.Channels.8.0.0/lib/netstandard2.1/System.Threading.Channels.dll", "Packages/com.unity.burst/Unity.Burst.Unsafe.dll", "Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll", "Packages/com.unity.nuget.newtonsoft-json/Runtime/AOT/Newtonsoft.Json.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.2D.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.InputSystem.ForUI.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/R3.Unity.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.Shaders.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.ResourceManager.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.IK.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.SpriteShape.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Core.Runtime.Shared.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.TextMeshPro.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UnityEngine.UI.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Core.ShaderLibrary.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.Linq.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.GPUDriven.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.ScriptableBuildPipeline.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Collections.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/LitMotion.Extensions.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Addressables.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.DOTween.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.InternalAPIEngineBridge.001.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.Config.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.Animation.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/LitMotion.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Profiling.Core.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Domain_Reload.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.TextMeshPro.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/R3.Unity.TextMeshPro.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.Tilemap.Extras.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.Common.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Mathematics.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Universal.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.Addressables.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.2D.PixelPerfect.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Burst.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.RenderPipelines.Core.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.Rendering.LightTransport.Runtime.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/Unity.InputSystem.dll", "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af/UniTask.dll"], "References": [], "AllowUnsafeCode": false, "AnalyzerConfigPath": "", "LanguageVersion": "9.0", "UseDeterministicCompilation": false, "SuppressCompilerWarnings": false, "Analyzers": ["Packages/com.unity.project-auditor/RoslynAnalyzers/Domain_Reload_Analyzer.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"], "AdditionalFiles": [], "BclDirectories": ["/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/ref/2.1.0", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/Extensions/2.0.0", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/EditorExtensions", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx"], "CustomCompilerOptions": ["/nowarn:0169", "/nowarn:0649", "/nowarn:0282", "/nowarn:1701", "/nowarn:1702"], "DebugIndex": 0, "SkipCodeGen": false, "Path": "/Users/<USER>/Desktop/Projects/mup_match3/mup.game.match3"}], "DotnetRuntimePath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetCoreRuntime", "DotnetRoslynPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/DotNetSdkRoslyn", "MovedFromExtractorPath": "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "OutputDirectory": "Temp/UnityTempFile-da47089b971b04966a04273c3aa4a2af", "Debug": false, "BuildTarget": "StandaloneOSX", "Localization": "en-US", "BuildPlayerDataOutput": "Library/BuildPlayerData/Editor", "ExtractRuntimeInitializeOnLoads": false, "EnableDiagnostics": false, "EmitInfoForScriptUpdater": false, "AssembliesToScanForTypeDB": ["Assets/Packages/Microsoft.Bcl.AsyncInterfaces.6.0.0/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "Assets/Packages/Microsoft.Bcl.TimeProvider.8.0.0/lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll", "Assets/Packages/R3.1.3.0/lib/netstandard2.1/R3.dll", "Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1/System.ComponentModel.Annotations.dll", "Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "Assets/Packages/System.Threading.Channels.8.0.0/lib/netstandard2.1/System.Threading.Channels.dll", "Library/ScriptAssemblies/Assembly-CSharp.dll", "Library/ScriptAssemblies/Domain_Reload.dll", "Library/ScriptAssemblies/LitMotion.dll", "Library/ScriptAssemblies/LitMotion.Extensions.dll", "Library/ScriptAssemblies/R3.Unity.dll", "Library/ScriptAssemblies/R3.Unity.TextMeshPro.dll", "Library/ScriptAssemblies/R3.Unity.XRInteractionToolkit.dll", "Library/ScriptAssemblies/UniTask.Addressables.dll", "Library/ScriptAssemblies/UniTask.dll", "Library/ScriptAssemblies/UniTask.DOTween.dll", "Library/ScriptAssemblies/UniTask.Linq.dll", "Library/ScriptAssemblies/UniTask.TextMeshPro.dll", "Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll", "Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll", "Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll", "Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll", "Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll", "Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll", "Library/ScriptAssemblies/Unity.Addressables.dll", "Library/ScriptAssemblies/Unity.Burst.dll", "Library/ScriptAssemblies/Unity.Collections.dll", "Library/ScriptAssemblies/Unity.InputSystem.dll", "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll", "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll", "Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll", "Library/ScriptAssemblies/Unity.Mathematics.dll", "Library/ScriptAssemblies/Unity.PerformanceTesting.dll", "Library/ScriptAssemblies/Unity.Profiling.Core.dll", "Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll", "Library/ScriptAssemblies/Unity.ResourceManager.dll", "Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll", "Library/ScriptAssemblies/Unity.TextMeshPro.dll", "Library/ScriptAssemblies/UnityEngine.TestRunner.dll", "Library/ScriptAssemblies/UnityEngine.UI.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll", "Packages/com.unity.burst/Unity.Burst.Unsafe.dll", "Packages/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "Packages/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Mdb.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Pdb.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Rocks.dll", "Packages/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll"], "SearchPaths": ["/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/Managed/UnityEngine", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/EditorExtensions", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/Extensions/2.0.0", "/Applications/Unity/Hub/Editor/6000.0.51f1/Unity.app/Contents/NetStandard/ref/2.1.0", "Assets/Packages/Microsoft.Bcl.AsyncInterfaces.6.0.0/lib/netstandard2.1", "Assets/Packages/Microsoft.Bcl.TimeProvider.8.0.0/lib/netstandard2.0", "Assets/Packages/R3.1.3.0/lib/netstandard2.1", "Assets/Packages/System.ComponentModel.Annotations.5.0.0/lib/netstandard2.1", "Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0", "Assets/Packages/System.Threading.Channels.8.0.0/lib/netstandard2.1", "Library/ScriptAssemblies", "Packages/com.unity.burst", "Packages/com.unity.burst/Unity.Burst.CodeGen", "Packages/com.unity.collections/Unity.Collections.LowLevel.ILSupport", "Packages/com.unity.ext.nunit/net40/unity-custom", "Packages/com.unity.nuget.mono-cecil", "Packages/com.unity.nuget.newtonsoft-json/Runtime"]}}